import React, { useState, useEffect } from 'react';
import { Layout, Home, CheckSquare, Calendar, Settings, Users, FolderKanban, Columns, LayoutGrid, List, Clock, Palette, UserCheck, Bar<PERSON><PERSON>3, Loader2 } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useSupabaseStore } from '../store/useSupabaseStore';
import { useInitializeSupabaseStore } from '../hooks/useSupabaseStore';
import UserGroupManager from './UserGroupManager';
import Timeline from './Timeline';
import Dashboard from './Dashboard';
import ProjectManager from './ProjectManager';
import ColumnManager from './ColumnManager';
import SupabaseSettingsPanel from './SupabaseSettings';
import KanbanBoard from './KanbanBoard';
import TaskTreeSidebar from './TaskTreeSidebar';
import TaskListView from './TaskListView';
import SkillsetGroupManager from './SkillsetGroupManager';
import UserManager from './UserManager';
import ResourceCalendar from './ResourceCalendar';
import DebugPanel from './DebugPanel';

export default function SupabaseSidebar() {
  const { profile, isAdmin } = useAuth();
  const { tasksViewMode, setTasksViewMode, loading } = useSupabaseStore();
  const { initialized } = useInitializeSupabaseStore();
  const [activeSection, setActiveSection] = useState('dashboard');

  // Show loading state while initializing
  if (!initialized) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading your workspace...</p>
        </div>
      </div>
    );
  }

  const menuItems = [
    { icon: Home, label: 'Dashboard', id: 'dashboard' },
    { icon: CheckSquare, label: 'Tasks', id: 'tasks' },
    { icon: Calendar, label: 'Timeline', id: 'timeline' },
    { icon: FolderKanban, label: 'Projects', id: 'projects' },
    { icon: Users, label: 'User Groups', id: 'groups' },
    { icon: UserCheck, label: 'Users', id: 'users' },
    { icon: Palette, label: 'Skillsets', id: 'skillsets' },
    { icon: Clock, label: 'Resource Calendar', id: 'calendar' },
    { icon: Columns, label: 'Columns', id: 'columns' },
    { icon: Settings, label: 'Settings', id: 'settings' },
  ];

  const renderContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return (
          <div className="fixed inset-0 left-64 bg-gray-50 overflow-auto">
            <Dashboard />
          </div>
        );
      case 'groups':
        return (
          <div className="fixed inset-0 left-64 bg-gray-900 overflow-auto">
            <UserGroupManager />
          </div>
        );
      case 'timeline':
        return (
          <div className="fixed inset-0 left-64 bg-gray-50 overflow-auto">
            <Timeline />
          </div>
        );
      case 'projects':
        return (
          <div className="fixed inset-0 left-64 bg-gray-900 overflow-auto">
            <ProjectManager />
          </div>
        );
      case 'columns':
        return (
          <div className="fixed inset-0 left-64 bg-gray-900 overflow-auto">
            <ColumnManager />
          </div>
        );
      case 'settings':
        return (
          <div className="fixed inset-0 left-64 bg-gray-900 overflow-auto">
            <SupabaseSettingsPanel />
          </div>
        );
      case 'users':
        return (
          <div className="fixed inset-0 left-64 bg-gray-900 overflow-auto">
            <UserManager />
          </div>
        );
      case 'skillsets':
        return (
          <div className="fixed inset-0 left-64 bg-gray-900 overflow-auto">
            <SkillsetGroupManager />
          </div>
        );
      case 'calendar':
        return (
          <div className="fixed inset-0 left-64 bg-gray-50 overflow-auto">
            <ResourceCalendar />
          </div>
        );
      case 'tasks':
        return (
          <div className="flex h-screen">
            <div className="w-64 bg-gray-900 fixed left-0 top-0 h-full z-10">
              {/* Sidebar content is rendered separately */}
            </div>
            <div className="w-80 bg-gray-800 fixed left-64 top-0 h-full overflow-y-auto z-10">
              <TaskTreeSidebar />
            </div>
            <div className="flex-1 ml-[352px] bg-gray-50 overflow-hidden">
              <div className="h-full flex flex-col">
                <div className="flex justify-between items-center p-6 pb-4 flex-shrink-0">
                  <h1 className="text-2xl font-bold text-gray-900">Tasks</h1>
                  <div className="flex items-center gap-4">
                    {loading.tasks && (
                      <div className="flex items-center gap-2 text-gray-600">
                        <Loader2 className="w-4 h-4 animate-spin" />
                        <span className="text-sm">Syncing...</span>
                      </div>
                    )}
                    <div className="flex bg-gray-200 rounded-lg p-1">
                      <button
                        onClick={() => setTasksViewMode('kanban')}
                        className={`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${
                          tasksViewMode === 'kanban'
                            ? 'bg-white text-gray-900 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                      >
                        <LayoutGrid className="w-4 h-4" />
                        Kanban
                      </button>
                      <button
                        onClick={() => setTasksViewMode('list')}
                        className={`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${
                          tasksViewMode === 'list'
                            ? 'bg-white text-gray-900 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                      >
                        <List className="w-4 h-4" />
                        List
                      </button>
                    </div>
                  </div>
                </div>
                <div className="flex-1 overflow-hidden">
                  {tasksViewMode === 'kanban' ? <KanbanBoard /> : <TaskListView />}
                </div>
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <>
      <div className="w-64 bg-gray-900 h-screen fixed left-0 top-0 text-white p-4 overflow-y-auto z-10">
        <div className="flex items-center gap-2 mb-8">
          <Layout className="w-8 h-8 text-blue-400" />
          <h1 className="text-xl font-bold">TaskFlow</h1>
        </div>
        
        {/* User Info */}
        <div className="mb-6 p-3 bg-gray-800 rounded-lg">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center text-sm font-semibold">
              {profile?.name?.charAt(0) || 'U'}
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-white truncate">
                {profile?.name || 'User'}
              </p>
              <p className="text-xs text-gray-400 truncate">
                {profile?.email}
              </p>
            </div>
          </div>
          {isAdmin && (
            <div className="mt-2">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
                Administrator
              </span>
            </div>
          )}
        </div>
        
        <nav className="space-y-2">
          {menuItems.map(({ icon: Icon, label, id }) => (
            <button
              key={id}
              onClick={() => setActiveSection(id)}
              className={`flex items-center gap-3 w-full p-3 rounded-lg transition-colors ${
                activeSection === id ? 'bg-gray-800' : 'hover:bg-gray-800'
              }`}
            >
              <Icon className="w-5 h-5" />
              <span>{label}</span>
            </button>
          ))}
        </nav>
      </div>

      {renderContent()}
    </>
  );
}
