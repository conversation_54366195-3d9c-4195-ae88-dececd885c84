import React from 'react';
import { useSupabaseStore } from '../store/useSupabaseStore';

export default function DebugPanel() {
  const { tasks, projects, users, columns, skillsetGroups, userGroups, loading, initialized } = useSupabaseStore();

  return (
    <div className="fixed top-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg z-50 max-w-md">
      <h3 className="font-bold text-lg mb-2">Debug Info</h3>
      <div className="text-sm space-y-1">
        <div>Initialized: {initialized ? 'Yes' : 'No'}</div>
        <div>Tasks: {tasks.length}</div>
        <div>Projects: {projects.length}</div>
        <div>Users: {users.length}</div>
        <div>Columns: {columns.length}</div>
        <div>Skillsets: {skillsetGroups.length}</div>
        <div>User Groups: {userGroups.length}</div>
        <div>Loading: {JSON.stringify(loading)}</div>
        
        {users.length > 0 && (
          <div>
            <div className="font-semibold mt-2">Users:</div>
            {users.map(user => (
              <div key={user.id} className="ml-2">
                {user.name} ({user.email})
              </div>
            ))}
          </div>
        )}
        
        {columns.length > 0 && (
          <div>
            <div className="font-semibold mt-2">Columns:</div>
            {columns.map(col => (
              <div key={col.id} className="ml-2">
                {col.id}: {col.title}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
