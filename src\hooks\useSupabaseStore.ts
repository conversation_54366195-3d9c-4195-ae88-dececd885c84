import { useEffect } from 'react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import { useAuth } from './useAuth';

export const useInitializeSupabaseStore = () => {
  const { user, initialized: authInitialized } = useAuth();
  const { initialize, initialized: storeInitialized, cleanupSubscriptions } = useSupabaseStore();

  useEffect(() => {
    if (authInitialized && user && !storeInitialized) {
      initialize();
    }
    
    // Cleanup subscriptions when user logs out or component unmounts
    return () => {
      if (!user) {
        cleanupSubscriptions();
      }
    };
  }, [authInitialized, user, storeInitialized, initialize, cleanupSubscriptions]);

  useEffect(() => {
    // Cleanup on unmount
    return () => {
      cleanupSubscriptions();
    };
  }, [cleanupSubscriptions]);

  return { initialized: storeInitialized };
};
