import React, { useState } from 'react';
import { useStore } from '../store/useStore';
import { Plus, Edit2, Trash2, Save, X, Palette } from 'lucide-react';
import { SkillsetGroup } from '../types';

export default function SkillsetGroupManager() {
  const { skillsetGroups, addSkillsetGroup, updateSkillsetGroup, deleteSkillsetGroup } = useStore();
  const [showForm, setShowForm] = useState(false);
  const [editingSkillset, setEditingSkillset] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    color: 'bg-blue-500',
  });

  const colorOptions = [
    'bg-blue-500',
    'bg-green-500',
    'bg-purple-500',
    'bg-red-500',
    'bg-yellow-500',
    'bg-indigo-500',
    'bg-pink-500',
    'bg-gray-500',
    'bg-orange-500',
    'bg-teal-500'
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editingSkillset) {
      updateSkillsetGroup(editingSkillset, formData);
      setEditingSkillset(null);
    } else {
      addSkillsetGroup(formData);
    }
    setFormData({ name: '', description: '', color: 'bg-blue-500' });
    setShowForm(false);
  };

  const handleEdit = (skillset: SkillsetGroup) => {
    setFormData({
      name: skillset.name,
      description: skillset.description || '',
      color: skillset.color,
    });
    setEditingSkillset(skillset.id);
    setShowForm(true);
  };

  const handleCancel = () => {
    setFormData({ name: '', description: '', color: 'bg-blue-500' });
    setEditingSkillset(null);
    setShowForm(false);
  };

  const handleDelete = (id: string) => {
    if (window.confirm('Are you sure you want to delete this skillset group? This will remove it from all users and tasks.')) {
      deleteSkillsetGroup(id);
    }
  };

  return (
    <div className="p-6 bg-gray-900 text-white min-h-screen">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">Skillset Groups</h1>
            <p className="text-gray-400">Manage skillset groups for resource planning and capacity management</p>
          </div>
          <button
            onClick={() => setShowForm(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Skillset Group
          </button>
        </div>

        {/* Skillset Groups List */}
        <div className="grid gap-4 mb-6">
          {skillsetGroups.length === 0 ? (
            <div className="text-center py-12">
              <Palette className="w-12 h-12 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-400 mb-4">No skillset groups defined yet</p>
              <button
                onClick={() => setShowForm(true)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Create your first skillset group
              </button>
            </div>
          ) : (
            skillsetGroups.map((skillset) => (
              <div
                key={skillset.id}
                className="bg-gray-800 p-4 rounded-lg flex items-center justify-between group"
              >
                <div className="flex items-center gap-4">
                  <div className={`w-4 h-4 rounded-full ${skillset.color}`} />
                  <div>
                    <h3 className="text-white font-medium">{skillset.name}</h3>
                    {skillset.description && (
                      <p className="text-gray-400 text-sm">{skillset.description}</p>
                    )}
                    <p className="text-gray-500 text-xs">
                      Created: {new Date(skillset.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <div className="flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <button
                    onClick={() => handleEdit(skillset)}
                    className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
                  >
                    <Edit2 className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(skillset.id)}
                    className="p-2 text-gray-400 hover:text-red-400 hover:bg-gray-700 rounded"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Form Modal */}
        {showForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">
                  {editingSkillset ? 'Edit Skillset Group' : 'Create New Skillset Group'}
                </h3>
                <button onClick={handleCancel} className="text-gray-400 hover:text-white">
                  <X className="w-5 h-5" />
                </button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Name</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg p-2 text-white"
                    placeholder="e.g., HTML, Image Work, ESP Integrations"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Description (Optional)</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg p-2 text-white h-20"
                    placeholder="Brief description of this skillset..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Color</label>
                  <div className="flex gap-2 flex-wrap">
                    {colorOptions.map((color) => (
                      <button
                        key={color}
                        type="button"
                        onClick={() => setFormData({ ...formData, color })}
                        className={`w-8 h-8 rounded-full ${color} ${
                          formData.color === color ? 'ring-2 ring-white' : ''
                        }`}
                      />
                    ))}
                  </div>
                </div>

                <div className="flex gap-3 pt-4">
                  <button
                    type="submit"
                    className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 flex items-center justify-center gap-2"
                  >
                    <Save className="w-4 h-4" />
                    {editingSkillset ? 'Update' : 'Create'}
                  </button>
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
