import React, { useState, useRef, useEffect } from 'react';
import { useStore } from '../store/useStore';
import { Plus, Edit2, Trash2, Folder, GripVertical } from 'lucide-react';
import FolderTreeItem from './FolderTreeItem';
import { Project, Folder as FolderType } from '../types';

const COLORS = [
  'bg-blue-500',
  'bg-purple-500',
  'bg-pink-500',
  'bg-yellow-500',
  'bg-green-500',
  'bg-indigo-500',
];

const MIN_SIDEBAR_WIDTH = 200;
const MAX_SIDEBAR_WIDTH = 600;

export default function ProjectManager() {
  const { projects, folders, addProject, updateProject, deleteProject, addFolder, updateFolder, deleteFolder } = useStore();
  const [showProjectForm, setShowProjectForm] = useState(false);
  const [showFolderForm, setShowFolderForm] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [editingFolder, setEditingFolder] = useState<FolderType | null>(null);
  const [selectedFolderId, setSelectedFolderId] = useState<string | undefined>(undefined);
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const [sidebarWidth, setSidebarWidth] = useState(256);
  const [isResizing, setIsResizing] = useState(false);
  const resizeTimeoutRef = useRef<number | null>(null);
  const [parentFolderId, setParentFolderId] = useState<string | undefined>(undefined);

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    color: COLORS[0],
    folderId: undefined as string | undefined,
  });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing) return;

      if (resizeTimeoutRef.current !== null) {
        window.cancelAnimationFrame(resizeTimeoutRef.current);
      }

      resizeTimeoutRef.current = window.requestAnimationFrame(() => {
        const newWidth = e.clientX;
        if (newWidth >= MIN_SIDEBAR_WIDTH && newWidth <= MAX_SIDEBAR_WIDTH) {
          setSidebarWidth(newWidth);
        }
      });
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      if (resizeTimeoutRef.current !== null) {
        window.cancelAnimationFrame(resizeTimeoutRef.current);
        resizeTimeoutRef.current = null;
      }
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      if (resizeTimeoutRef.current !== null) {
        window.cancelAnimationFrame(resizeTimeoutRef.current);
        resizeTimeoutRef.current = null;
      }
    };
  }, [isResizing]);

  const handleProjectSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editingProject) {
      updateProject(editingProject.id, formData);
      setEditingProject(null);
    } else {
      addProject({ ...formData, folderId: parentFolderId });
    }
    setFormData({ name: '', description: '', color: COLORS[0], folderId: undefined });
    setShowProjectForm(false);
    setParentFolderId(undefined);
  };

  const handleFolderSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editingFolder) {
      updateFolder(editingFolder.id, { name: formData.name });
      setEditingFolder(null);
    } else {
      addFolder({ 
        name: formData.name, 
        parentId: parentFolderId 
      });
    }
    setFormData({ name: '', description: '', color: COLORS[0], folderId: undefined });
    setShowFolderForm(false);
    setParentFolderId(undefined);
  };

  const handleToggleFolder = (folderId: string) => {
    setExpandedFolders(prev => {
      const newExpanded = new Set(prev);
      if (newExpanded.has(folderId)) {
        newExpanded.delete(folderId);
      } else {
        newExpanded.add(folderId);
      }
      return newExpanded;
    });
  };

  return (
    <div className="flex h-full bg-gray-900">
      <div className="w-full max-w-5xl mx-auto p-6">
        <div className="bg-gray-800 rounded-lg shadow-lg">
          <div className="p-4 border-b border-gray-700">
            <div className="flex items-center justify-between mb-2">
              <h2 className="text-lg font-semibold text-white">Projects & Folders</h2>
              <div className="flex gap-2">
                <button
                  onClick={() => {
                    setParentFolderId(selectedFolderId);
                    setShowProjectForm(true);
                  }}
                  className="p-1.5 hover:bg-gray-700 rounded-lg text-gray-400 hover:text-white transition-colors"
                  title="Add Project"
                >
                  <Plus className="w-4 h-4" />
                </button>
                <button
                  onClick={() => {
                    setParentFolderId(selectedFolderId);
                    setShowFolderForm(true);
                  }}
                  className="p-1.5 hover:bg-gray-700 rounded-lg text-gray-400 hover:text-white transition-colors"
                  title="Add Folder"
                >
                  <Folder className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          <div className="min-h-[400px]">
            <FolderTreeItem
              level={0}
              onSelectFolder={setSelectedFolderId}
              expandedFolders={expandedFolders}
              onToggleFolder={handleToggleFolder}
              selectedFolderId={selectedFolderId}
              onAddSubfolder={(folderId) => {
                setParentFolderId(folderId);
                setShowFolderForm(true);
              }}
              onAddProject={(folderId) => {
                setParentFolderId(folderId);
                setShowProjectForm(true);
              }}
              onEditFolder={(folder) => {
                setEditingFolder(folder);
                setFormData({ ...formData, name: folder.name });
                setShowFolderForm(true);
              }}
              onDeleteFolder={deleteFolder}
              onEditProject={(project) => {
                setEditingProject(project);
                setFormData({
                  name: project.name,
                  description: project.description,
                  color: project.color,
                  folderId: project.folderId,
                });
                setShowProjectForm(true);
              }}
              onDeleteProject={deleteProject}
              projects={projects}
              folders={folders}
            />
          </div>
        </div>
      </div>

      {/* Modal Forms */}
      {showProjectForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 p-6 rounded-lg w-96">
            <h3 className="text-lg font-semibold mb-4 text-white">
              {editingProject ? 'Edit Project' : 'Create Project'}
              {parentFolderId && folders.find(f => f.id === parentFolderId) && (
                <span className="block text-sm text-gray-400 mt-1">
                  in folder: {folders.find(f => f.id === parentFolderId)?.name}
                </span>
              )}
            </h3>
            <form onSubmit={handleProjectSubmit} className="space-y-4">
              <div>
                <label className="block text-sm mb-1 text-gray-300">Name</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 rounded-lg text-white"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm mb-1 text-gray-300">Description</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 rounded-lg text-white"
                  rows={3}
                />
              </div>
              
              <div>
                <label className="block text-sm mb-1 text-gray-300">Color</label>
                <div className="flex gap-2">
                  {COLORS.map((color) => (
                    <button
                      key={color}
                      type="button"
                      onClick={() => setFormData({ ...formData, color })}
                      className={`w-8 h-8 rounded-full ${color} ${
                        formData.color === color ? 'ring-2 ring-offset-2 ring-blue-400' : ''
                      }`}
                    />
                  ))}
                </div>
              </div>
              
              <div className="flex justify-end gap-2">
                <button
                  type="button"
                  onClick={() => {
                    setShowProjectForm(false);
                    setEditingProject(null);
                    setParentFolderId(undefined);
                  }}
                  className="px-4 py-2 text-gray-300 hover:bg-gray-700 rounded-lg"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  {editingProject ? 'Update' : 'Create'} Project
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {showFolderForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 p-6 rounded-lg w-96">
            <h3 className="text-lg font-semibold mb-4 text-white">
              {editingFolder ? 'Edit Folder' : 'Create Folder'}
              {parentFolderId && folders.find(f => f.id === parentFolderId) && (
                <span className="block text-sm text-gray-400 mt-1">
                  in folder: {folders.find(f => f.id === parentFolderId)?.name}
                </span>
              )}
            </h3>
            <form onSubmit={handleFolderSubmit} className="space-y-4">
              <div>
                <label className="block text-sm mb-1 text-gray-300">Name</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 rounded-lg text-white"
                  required
                />
              </div>
              
              <div className="flex justify-end gap-2">
                <button
                  type="button"
                  onClick={() => {
                    setShowFolderForm(false);
                    setEditingFolder(null);
                    setParentFolderId(undefined);
                  }}
                  className="px-4 py-2 text-gray-300 hover:bg-gray-700 rounded-lg"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  {editingFolder ? 'Update' : 'Create'} Folder
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}