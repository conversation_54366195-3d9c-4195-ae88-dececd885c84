import React, { useState } from 'react';
import { Subtask, Task } from '../types';
import { Plus, Trash2, Check, Edit2 } from 'lucide-react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import SubtaskForm from './SubtaskForm';
import {formatValue} from './formatValue';


interface SubtaskListProps {
  taskId: string;
  subtasks: Subtask[];
  parentTaskStatus: Task['status'];
  onSubtaskUpdate: () => void;
}

export default function SubtaskList({ taskId, subtasks, parentTaskStatus, onSubtaskUpdate }: SubtaskListProps) {
  const { columns, users } = useSupabaseStore();

  // TODO: Implement subtask management in Supabase store
  const addSubtask = (taskId: string, subtask: Omit<Subtask, 'id'>) => {
    console.log('TODO: Implement addSubtask in Supabase store', { taskId, subtask });
  };

  const updateSubtask = (taskId: string, subtaskId: string, updates: Partial<Subtask>) => {
    console.log('TODO: Implement updateSubtask in Supabase store', { taskId, subtaskId, updates });
  };

  const deleteSubtask = (taskId: string, subtaskId: string) => {
    console.log('TODO: Implement deleteSubtask in Supabase store', { taskId, subtaskId });
  };
  const [showForm, setShowForm] = useState(false);
  const [editingSubtask, setEditingSubtask] = useState<Subtask | null>(null);

  const handleAddSubtask = () => {
    if (subtasks.length < 20) {
      setEditingSubtask(null);
      setShowForm(true);
    }
  };

  const handleEditSubtask = (subtask: Subtask) => {
    setEditingSubtask(subtask);
    setShowForm(true);
  };

  const handleSubmit = (subtaskData: Omit<Subtask, 'id'>) => {
    if (editingSubtask) {
      updateSubtask(taskId, editingSubtask.id, subtaskData);
    } else {
      addSubtask(taskId, subtaskData);
    }
    setShowForm(false);
    setEditingSubtask(null);
    onSubtaskUpdate(); // Trigger parent update
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Subtasks ({subtasks.length}/20)</h3>
        <button
          onClick={handleAddSubtask}
          disabled={subtasks.length >= 20}
          className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Add Subtask
        </button>
      </div>

      <div className="space-y-2">
        {subtasks.map((subtask) => {
          console.log("Rendering Subtask:", subtask); // Log each subtask being rendered
          return (
          <div
            key={subtask.id}
            className="flex items-center gap-3 p-3 hover:bg-gray-50 rounded-lg group border"
          >
            <button
              onClick={() => {
                updateSubtask(taskId, subtask.id, { completed: !subtask.completed });
                onSubtaskUpdate(); // Trigger parent update
              }}
              className={`w-5 h-5 rounded border flex items-center justify-center flex-shrink-0 ${
                subtask.completed ? 'bg-blue-600 border-blue-600' : 'border-gray-300'
              }`}
            >
              {subtask.completed && <Check className="w-3 h-3 text-white" />}
            </button>

            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h4 className="font-medium truncate">{subtask.title}</h4>
                <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <button
                    onClick={() => handleEditSubtask(subtask)}
                    className="p-1 hover:bg-gray-100 rounded"
                  >
                    <Edit2 className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => {
                      deleteSubtask(taskId, subtask.id);
                      onSubtaskUpdate(); // Trigger parent update {subtask.assignedUserId}
                    }}
                    className="p-1 hover:bg-red-50 rounded text-red-600"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
              {subtask.assignedUserId && (
                <p className="text-sm text-gray-500 mt-1">
                  Assigned to: {formatValue('assignedUserId', subtask.assignedUserId,columns, users)}
                </p>
              )}
            </div>
          </div>
          );
        })}
      </div>

      {showForm && (
        <SubtaskForm
          onSubmit={handleSubmit}
          onClose={() => {
            setShowForm(false);
            setEditingSubtask(null);
          }}
          initialData={editingSubtask}
          taskId={taskId}
          parentTaskStatus={parentTaskStatus}
        />
      )}

      {subtasks.length >= 20 && (
        <p className="text-sm text-yellow-600">
          Maximum number of subtasks (20) reached
        </p>
      )}
    </div>
  );
}