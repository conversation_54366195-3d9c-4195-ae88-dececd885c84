import React, { useState } from 'react';
import { Subtask, Task } from '../types';
import { X } from 'lucide-react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import TaskHistory from './TaskHistory';
import TaskComments from './TaskComments';
import TaskDurationStats from './TaskDurationStats';

interface SubtaskFormProps {
  onSubmit: (subtask: Omit<Subtask, 'id'>) => void;
  onClose: () => void;
  initialData?: Subtask | null;
  taskId: string;
  parentTaskStatus: Task['status'];
}

export default function SubtaskForm({ onSubmit, onClose, initialData, taskId, parentTaskStatus }: SubtaskFormProps) {
  const { users, userGroups } = useSupabaseStore();
  const [formData, setFormData] = useState({
    title: initialData?.title || '',
    description: initialData?.description || '',
    completed: initialData?.completed || false,
    assignedUserId: initialData?.assignedUserId || '',
    priority: initialData?.priority || 'medium' as Task['priority'],
    startDate: initialData?.startDate || '',
    dueDate: initialData?.dueDate || '',
    assignedGroups: initialData?.assignedGroups || [] as string[],
    owner: initialData?.owner || '',
    status: parentTaskStatus, // Always use parent task's status
    comments: initialData?.comments || [],
    history: initialData?.history || [],
    durations: initialData?.durations || [],
  });

  console.log("Initial Data for Subtask:", initialData);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Submitting subtask form:", formData);
    onSubmit({
      ...formData,
      comments: formData.comments || [],
    history: formData.history || [],
    durations: formData.durations || []
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-[1280px] max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold">
            {initialData ? 'Edit Subtask' : 'Create Subtask'}
          </h3>
          <button onClick={onClose} className="p-1 hover:bg-gray-100 rounded">
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="grid grid-cols-3 gap-6">
          <div className="col-span-2">
            {initialData && (
              <TaskDurationStats
                durations={initialData.durations || []}
                currentStatus={parentTaskStatus}
              />
            )}

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Title</label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  className="w-full px-3 py-2 border rounded-lg"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Description</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="w-full px-3 py-2 border rounded-lg"
                  rows={3}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Priority</label>
                <select
                  value={formData.priority}
                  onChange={(e) => setFormData({ ...formData, priority: e.target.value as Task['priority'] })}
                  className="w-full px-3 py-2 border rounded-lg"
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Assigned User</label>
                <select
                  value={formData.assignedUserId}
                  onChange={(e) => setFormData({ ...formData, assignedUserId: e.target.value })}
                  className="w-full px-3 py-2 border rounded-lg"
                >
                  <option value="">Select user</option>
                  {users.map((user) => (
                    <option key={user.id} value={user.id}>
                      {user.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Start Date</label>
                  <input
                    type="date"
                    value={formData.startDate}
                    onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Due Date</label>
                  <input
                    type="date"
                    value={formData.dueDate}
                    onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Assigned Groups</label>
                <div className="space-y-2">
                  {userGroups.map((group) => (
                    <label key={group.id} className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={formData.assignedGroups.includes(group.id)}
                        onChange={(e) => {
                          const newGroups = e.target.checked
                            ? [...formData.assignedGroups, group.id]
                            : formData.assignedGroups.filter(id => id !== group.id);
                          setFormData({ ...formData, assignedGroups: newGroups });
                        }}
                      />
                      <span className="text-sm">{group.name}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Owner Group</label>
                <select
                  value={formData.owner}
                  onChange={(e) => setFormData({ ...formData, owner: e.target.value })}
                  className="w-full px-3 py-2 border rounded-lg"
                >
                  <option value="">Select owner group</option>
                  {userGroups.map((group) => (
                    <option key={group.id} value={group.id}>
                      {group.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex justify-end gap-2">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  {initialData ? 'Update' : 'Create'} Subtask
                </button>
              </div>
            </form>
          </div>

          {initialData && (
            <div className="space-y-6 h-[calc(100vh-8rem)] overflow-hidden">
              <div className="h-1/2 overflow-y-auto pr-4">
                <TaskHistory history={initialData.history || []} />
              </div>
              <div className="h-1/2 overflow-y-auto pr-4">
                <TaskComments taskId={taskId} subtaskId={initialData.id} comments={initialData.comments || []} />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}