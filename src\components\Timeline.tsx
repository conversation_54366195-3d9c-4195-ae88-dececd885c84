// eslint-disable-next-line @typescript-eslint/no-unused-vars
import React, { useState } from 'react';
import { useStore } from '../store/useStore';
import { format, addDays, differenceInDays, parseISO, isValid } from 'date-fns';
import { Calendar, Filter, Eye, EyeOff } from 'lucide-react';

export default function Timeline() {
  const { tasks, projects, userGroups } = useStore();
  const [selectedProject, setSelectedProject] = useState<string>('all');
  const [selectedGroup, setSelectedGroup] = useState<string>('all');
  const [hideTaskLevel, setHideTaskLevel] = useState<boolean>(false);
  const today = new Date();
  
  const filteredTasks = tasks.filter(task => {
    const projectMatch = selectedProject === 'all' || task.projectId === selectedProject;
    const groupMatch = selectedGroup === 'all' ||
      (task.assignedGroups && task.assignedGroups.includes(selectedGroup));
    return projectMatch && groupMatch;
  });

  // Calculate project-level dates for compact view
  const getProjectDates = (projectId: string) => {
    const projectTasks = filteredTasks.filter(task => task.projectId === projectId);
    if (projectTasks.length === 0) return null;

    const taskDates = projectTasks
      .map(task => ({
        start: task.startDate ? parseISO(task.startDate) : today,
        end: task.dueDate ? parseISO(task.dueDate) : addDays(today, 7)
      }))
      .filter(({ start, end }) => isValid(start) && isValid(end));

    if (taskDates.length === 0) return null;

    const earliestStart = taskDates.reduce((min, curr) => curr.start < min ? curr.start : min, taskDates[0].start);
    const latestEnd = taskDates.reduce((max, curr) => curr.end > max ? curr.end : max, taskDates[0].end);

    return { start: earliestStart, end: latestEnd };
  };

  const dates = hideTaskLevel
    ? projects
        .map(project => getProjectDates(project.id))
        .filter(Boolean)
        .concat(
          // Add unassigned tasks if any
          filteredTasks.filter(task => !task.projectId).length > 0
            ? [{
                start: filteredTasks.filter(task => !task.projectId)
                  .map(task => task.startDate ? parseISO(task.startDate) : today)
                  .filter(isValid)
                  .reduce((min, curr) => curr < min ? curr : min, today),
                end: filteredTasks.filter(task => !task.projectId)
                  .map(task => task.dueDate ? parseISO(task.dueDate) : addDays(today, 7))
                  .filter(isValid)
                  .reduce((max, curr) => curr > max ? curr : max, addDays(today, 7))
              }]
            : []
        )
    : filteredTasks
        .map(task => ({
          start: task.startDate ? parseISO(task.startDate) : today,
          end: task.dueDate ? parseISO(task.dueDate) : addDays(today, 7)
        }))
        .filter(({ start, end }) => isValid(start) && isValid(end));

  const earliestDate = dates.length > 0 
    ? dates.reduce((min, curr) => curr.start < min ? curr.start : min, dates[0].start)
    : today;
    
  const latestDate = dates.length > 0
    ? dates.reduce((max, curr) => curr.end > max ? curr.end : max, dates[0].end)
    : addDays(today, 30);

  const totalDays = differenceInDays(latestDate, earliestDate) + 1;
  const daysArray = Array.from({ length: totalDays }, (_, i) => addDays(earliestDate, i));

  return (
    <div className="p-8">
      <div className="max-w-[1600px] mx-auto">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-2">
            <Calendar className="w-6 h-6 text-blue-600" />
            <h2 className="text-2xl font-bold">Timeline</h2>
          </div>

          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Filter className="w-5 h-5 text-gray-400" />
              <select
                value={selectedProject}
                onChange={(e) => setSelectedProject(e.target.value)}
                className="border rounded-lg px-3 py-2"
              >
                <option value="all">All Projects</option>
                {projects.map((project) => (
                  <option key={project.id} value={project.id}>
                    {project.name}
                  </option>
                ))}
              </select>

              <select
                value={selectedGroup}
                onChange={(e) => setSelectedGroup(e.target.value)}
                className="border rounded-lg px-3 py-2"
              >
                <option value="all">All Teams</option>
                {userGroups.map((group) => (
                  <option key={group.id} value={group.id}>
                    {group.name}
                  </option>
                ))}
              </select>
            </div>

            <button
              onClick={() => setHideTaskLevel(!hideTaskLevel)}
              className={`flex items-center gap-2 px-3 py-2 rounded-lg border transition-colors ${
                hideTaskLevel
                  ? 'bg-blue-50 border-blue-200 text-blue-700'
                  : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'
              }`}
              title={hideTaskLevel ? 'Show task details' : 'Hide task details'}
            >
              {hideTaskLevel ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
              <span className="text-sm">
                {hideTaskLevel ? 'Show Tasks' : 'Hide Tasks'}
              </span>
            </button>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm">
          <div className="overflow-x-auto">
            <div className="min-w-[800px]">
              <div className="sticky top-0 bg-white z-10 border-b">
                <div className="flex">
                  <div className="w-1/4 p-4 font-semibold">Task</div>
                  <div className="flex-1 flex">
                    {daysArray.map(date => (
                      <div
                        key={date.toISOString()}
                        className={`flex-1 p-2 text-center text-sm ${
                          format(date, 'yyyy-MM-dd') === format(today, 'yyyy-MM-dd')
                            ? 'bg-blue-50'
                            : ''
                        }`}
                      >
                        <div className="font-medium">{format(date, 'MMM d')}</div>
                        <div className="text-xs text-gray-500">{format(date, 'EEE')}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="relative">
                {hideTaskLevel ? (
                  // Project-level compact view
                  projects.map(project => {
                    const projectTasks = filteredTasks.filter(task => task.projectId === project.id);
                    if (projectTasks.length === 0) return null;

                    const projectDates = getProjectDates(project.id);
                    if (!projectDates) return null;

                    const startOffset = differenceInDays(projectDates.start, earliestDate);
                    const duration = differenceInDays(projectDates.end, projectDates.start) + 1;
                    const totalTasks = projectTasks.length;
                    const completedTasks = projectTasks.filter(task => task.status === 'done').length;
                    const inProgressTasks = projectTasks.filter(task => task.status === 'in-progress').length;

                    return (
                      <div key={project.id} className="flex items-center hover:bg-gray-50 mb-2">
                        <div className="sticky left-0 w-1/4 p-4 bg-white z-10">
                          <div className="flex items-center gap-2">
                            <div className={`w-3 h-3 rounded-full ${project.color}`} />
                            <h3 className="font-semibold">{project.name}</h3>
                          </div>
                          <div className="text-sm text-gray-600 mt-1">
                            {totalTasks} tasks • {completedTasks} done • {inProgressTasks} in progress
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            {format(projectDates.start, 'MMM d')} - {format(projectDates.end, 'MMM d')}
                            ({duration} days)
                          </div>
                        </div>
                        <div className="flex-1 flex items-center min-h-[5rem] px-[1px]">
                          <div
                            className={`h-10 rounded-lg ${project.color} bg-opacity-30 border-l-4 ${project.color.replace('bg-', 'border-')}`}
                            style={{
                              marginLeft: `${(startOffset / totalDays) * 100}%`,
                              width: `${(duration / totalDays) * 100}%`
                            }}
                          >
                            <div className="px-3 py-2 text-sm font-medium">
                              {project.name}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  // Original detailed task view
                  projects.map(project => {
                    const projectTasks = filteredTasks.filter(task => task.projectId === project.id);
                    if (projectTasks.length === 0) return null;

                    return (
                      <div key={project.id} className="mb-8">
                        <div className="sticky left-0 flex items-center gap-2 px-4 py-2 bg-gray-50 z-10">
                          <div className={`w-3 h-3 rounded-full ${project.color}`} />
                          <h3 className="font-semibold">{project.name}</h3>
                        </div>

                        {projectTasks.map(task => {
                        const startDate = task.startDate ? parseISO(task.startDate) : today;
                        const endDate = task.dueDate ? parseISO(task.dueDate) : addDays(today, 7);
                        
                        if (!isValid(startDate) || !isValid(endDate)) return null;

                        const startOffset = differenceInDays(startDate, earliestDate);
                        const duration = differenceInDays(endDate, startDate) + 1;
                        
                        return (
                          <div key={task.id} className="flex items-center hover:bg-gray-50">
                            <div className="sticky left-0 w-1/4 p-4 bg-white z-10">
                              <h4 className="font-medium">{task.title}</h4>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {task.assignedGroups?.map(groupId => {
                                  const group = userGroups.find(g => g.id === groupId);
                                  return group ? (
                                    <span
                                      key={groupId}
                                      className={`px-2 py-0.5 rounded-full text-xs ${group.color} bg-opacity-20`}
                                    >
                                      {group.name}
                                    </span>
                                  ) : null;
                                })}
                              </div>
                            </div>
                            <div className="flex-1 flex items-center min-h-[4rem] px-[1px]">
                              <div
                                className={`h-8 rounded-lg ${
                                  task.status === 'done'
                                    ? 'bg-green-200'
                                    : task.status === 'in-progress'
                                    ? 'bg-blue-200'
                                    : 'bg-gray-200'
                                }`}
                                style={{
                                  marginLeft: `${(startOffset / totalDays) * 100}%`,
                                  width: `${(duration / totalDays) * 100}%`
                                }}
                              >
                                <div className="px-2 py-1 text-sm truncate">
                                  {task.title}
                                </div>
                              </div>
                            </div>
                          </div>
                          );
                        })}
                      </div>
                    );
                  })
                )}

                {/* Handle unassigned tasks for both views */}
                {filteredTasks.filter(task => !task.projectId).length > 0 && (
                  hideTaskLevel ? (
                    // Compact view for unassigned tasks
                    (() => {
                      const unassignedTasks = filteredTasks.filter(task => !task.projectId);
                      const taskDates = unassignedTasks
                        .map(task => ({
                          start: task.startDate ? parseISO(task.startDate) : today,
                          end: task.dueDate ? parseISO(task.dueDate) : addDays(today, 7)
                        }))
                        .filter(({ start, end }) => isValid(start) && isValid(end));

                      if (taskDates.length === 0) return null;

                      const earliestStart = taskDates.reduce((min, curr) => curr.start < min ? curr.start : min, taskDates[0].start);
                      const latestEnd = taskDates.reduce((max, curr) => curr.end > max ? curr.end : max, taskDates[0].end);
                      const startOffset = differenceInDays(earliestStart, earliestDate);
                      const duration = differenceInDays(latestEnd, earliestStart) + 1;
                      const totalTasks = unassignedTasks.length;
                      const completedTasks = unassignedTasks.filter(task => task.status === 'done').length;
                      const inProgressTasks = unassignedTasks.filter(task => task.status === 'in-progress').length;

                      return (
                        <div className="flex items-center hover:bg-gray-50 mb-2">
                          <div className="sticky left-0 w-1/4 p-4 bg-white z-10">
                            <h3 className="font-semibold">Other Tasks</h3>
                            <div className="text-sm text-gray-600 mt-1">
                              {totalTasks} tasks • {completedTasks} done • {inProgressTasks} in progress
                            </div>
                            <div className="text-xs text-gray-500 mt-1">
                              {format(earliestStart, 'MMM d')} - {format(latestEnd, 'MMM d')}
                              ({duration} days)
                            </div>
                          </div>
                          <div className="flex-1 flex items-center min-h-[5rem] px-[1px]">
                            <div
                              className="h-10 rounded-lg bg-gray-300 border-l-4 border-gray-500"
                              style={{
                                marginLeft: `${(startOffset / totalDays) * 100}%`,
                                width: `${(duration / totalDays) * 100}%`
                              }}
                            >
                              <div className="px-3 py-2 text-sm font-medium">
                                Other Tasks
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })()
                  ) : (
                    // Detailed view for unassigned tasks
                    <div className="mb-8">
                      <div className="sticky left-0 flex items-center gap-2 px-4 py-2 bg-gray-50 z-10">
                        <h3 className="font-semibold">Other Tasks</h3>
                      </div>

                      {filteredTasks
                        .filter(task => !task.projectId)
                        .map(task => {
                        const startDate = task.startDate ? parseISO(task.startDate) : today;
                        const endDate = task.dueDate ? parseISO(task.dueDate) : addDays(today, 7);
                        
                        if (!isValid(startDate) || !isValid(endDate)) return null;

                        const startOffset = differenceInDays(startDate, earliestDate);
                        const duration = differenceInDays(endDate, startDate) + 1;
                        
                        return (
                          <div key={task.id} className="flex items-center hover:bg-gray-50">
                            <div className="sticky left-0 w-1/4 p-4 bg-white z-10">
                              <h4 className="font-medium">{task.title}</h4>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {task.assignedGroups?.map(groupId => {
                                  const group = userGroups.find(g => g.id === groupId);
                                  return group ? (
                                    <span
                                      key={groupId}
                                      className={`px-2 py-0.5 rounded-full text-xs ${group.color} bg-opacity-20`}
                                    >
                                      {group.name}
                                    </span>
                                  ) : null;
                                })}
                              </div>
                            </div>
                            <div className="flex-1 flex items-center min-h-[4rem] px-[1px]">
                              <div
                                className={`h-8 rounded-lg ${
                                  task.status === 'done'
                                    ? 'bg-green-200'
                                    : task.status === 'in-progress'
                                    ? 'bg-blue-200'
                                    : 'bg-gray-200'
                                }`}
                                style={{
                                  marginLeft: `${(startOffset / totalDays) * 100}%`,
                                  width: `${(duration / totalDays) * 100}%`
                                }}
                              >
                                <div className="px-2 py-1 text-sm truncate">
                                  {task.title}
                                </div>
                              </div>
                            </div>
                          </div>
                          );
                        })}
                    </div>
                  )
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}