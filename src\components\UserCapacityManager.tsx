import React, { useState } from 'react';
import { useStore } from '../store/useStore';
import { Clock, Save, X, Calendar, User } from 'lucide-react';
import { UserCapacity } from '../types';

interface UserCapacityManagerProps {
  userId: string;
  onClose: () => void;
}

export default function UserCapacityManager({ userId, onClose }: UserCapacityManagerProps) {
  const { users, skillsetGroups, userCapacities, addUserCapacity, updateUserCapacity } = useStore();
  
  const user = users.find(u => u.id === userId);
  const existingCapacity = userCapacities.find(c => c.userId === userId);
  
  const [formData, setFormData] = useState<Omit<UserCapacity, 'userId'>>({
    dailyHours: existingCapacity?.dailyHours || 8,
    weeklyHours: existingCapacity?.weeklyHours || 40,
    workingDays: existingCapacity?.workingDays || [1, 2, 3, 4, 5], // Monday to Friday
    effectiveFrom: existingCapacity?.effectiveFrom || new Date().toISOString().split('T')[0],
    effectiveTo: existingCapacity?.effectiveTo || undefined,
  });

  const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

  const handleWorkingDayToggle = (dayIndex: number) => {
    const newWorkingDays = formData.workingDays.includes(dayIndex)
      ? formData.workingDays.filter(d => d !== dayIndex)
      : [...formData.workingDays, dayIndex].sort();

    setFormData({ ...formData, workingDays: newWorkingDays });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const capacityData: UserCapacity = {
      userId,
      ...formData,
    };

    if (existingCapacity) {
      updateUserCapacity(userId, capacityData);
    } else {
      addUserCapacity(capacityData);
    }
    
    onClose();
  };

  if (!user) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <User className="w-6 h-6 text-blue-600" />
            <div>
              <h3 className="text-lg font-semibold">Capacity Management</h3>
              <p className="text-gray-600">{user.name}</p>
            </div>
          </div>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* General Capacity */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium mb-4 flex items-center gap-2">
              <Clock className="w-4 h-4" />
              General Capacity
            </h4>
            
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium mb-1">Daily Hours</label>
                <input
                  type="number"
                  min="0"
                  max="24"
                  step="0.5"
                  value={formData.dailyHours}
                  onChange={(e) => setFormData({ ...formData, dailyHours: parseFloat(e.target.value) })}
                  className="w-full border rounded-lg p-2"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Weekly Hours</label>
                <input
                  type="number"
                  min="0"
                  max="168"
                  step="0.5"
                  value={formData.weeklyHours}
                  onChange={(e) => setFormData({ ...formData, weeklyHours: parseFloat(e.target.value) })}
                  className="w-full border rounded-lg p-2"
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Working Days</label>
              <div className="flex flex-wrap gap-2">
                {dayNames.map((day, index) => (
                  <button
                    key={index}
                    type="button"
                    onClick={() => handleWorkingDayToggle(index)}
                    className={`px-3 py-1 rounded-full text-sm ${
                      formData.workingDays.includes(index)
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                  >
                    {day.slice(0, 3)}
                  </button>
                ))}
              </div>
            </div>
          </div>



          {/* Effective Period */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium mb-4 flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              Effective Period
            </h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">From Date</label>
                <input
                  type="date"
                  value={formData.effectiveFrom}
                  onChange={(e) => setFormData({ ...formData, effectiveFrom: e.target.value })}
                  className="w-full border rounded-lg p-2"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">To Date (Optional)</label>
                <input
                  type="date"
                  value={formData.effectiveTo || ''}
                  onChange={(e) => setFormData({ ...formData, effectiveTo: e.target.value || undefined })}
                  className="w-full border rounded-lg p-2"
                />
              </div>
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="submit"
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 flex items-center justify-center gap-2"
            >
              <Save className="w-4 h-4" />
              Save Capacity
            </button>
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
