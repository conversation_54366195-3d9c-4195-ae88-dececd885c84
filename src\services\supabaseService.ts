import { supabase, handleSupabaseError, getCurrentUser } from '../lib/supabase';
import { Database } from '../types/database';
import { 
  Task, 
  Project, 
  UserGroup, 
  KanbanColumn, 
  Folder, 
  TaskComment, 
  TaskHistoryEntry, 
  User, 
  SkillsetGroup, 
  UserCapacity, 
  TaskEffort 
} from '../types';

type Tables = Database['public']['Tables'];

// User Profiles
export const userProfileService = {
  async getProfile(userId: string) {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async updateProfile(userId: string, updates: Partial<Tables['user_profiles']['Update']>) {
    const { data, error } = await supabase
      .from('user_profiles')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', userId)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async createProfile(profile: Tables['user_profiles']['Insert']) {
    const { data, error } = await supabase
      .from('user_profiles')
      .insert(profile)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async getAllUsers() {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .order('name');
    
    if (error) handleSupabaseError(error);
    return data || [];
  }
};

// Tasks
export const taskService = {
  async getTasks() {
    const { data, error } = await supabase
      .from('tasks')
      .select(`
        *,
        task_comments(*),
        task_history(*)
      `)
      .order('created_at', { ascending: false });
    
    if (error) handleSupabaseError(error);
    return data || [];
  },

  async createTask(task: Omit<Tables['tasks']['Insert'], 'created_by'>) {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('tasks')
      .insert({ ...task, created_by: user.id })
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async updateTask(taskId: string, updates: Partial<Tables['tasks']['Update']>) {
    const { data, error } = await supabase
      .from('tasks')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', taskId)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async deleteTask(taskId: string) {
    const { error } = await supabase
      .from('tasks')
      .delete()
      .eq('id', taskId);
    
    if (error) handleSupabaseError(error);
  }
};

// Projects
export const projectService = {
  async getProjects() {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .order('name');
    
    if (error) handleSupabaseError(error);
    return data || [];
  },

  async createProject(project: Omit<Tables['projects']['Insert'], 'created_by'>) {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('projects')
      .insert({ ...project, created_by: user.id })
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async updateProject(projectId: string, updates: Partial<Tables['projects']['Update']>) {
    const { data, error } = await supabase
      .from('projects')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', projectId)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async deleteProject(projectId: string) {
    const { error } = await supabase
      .from('projects')
      .delete()
      .eq('id', projectId);
    
    if (error) handleSupabaseError(error);
  }
};

// User Groups
export const userGroupService = {
  async getUserGroups() {
    const { data, error } = await supabase
      .from('user_groups')
      .select('*')
      .order('name');
    
    if (error) handleSupabaseError(error);
    return data || [];
  },

  async createUserGroup(group: Omit<Tables['user_groups']['Insert'], 'created_by'>) {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('user_groups')
      .insert({ ...group, created_by: user.id })
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async updateUserGroup(groupId: string, updates: Partial<Tables['user_groups']['Update']>) {
    const { data, error } = await supabase
      .from('user_groups')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', groupId)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async deleteUserGroup(groupId: string) {
    const { error } = await supabase
      .from('user_groups')
      .delete()
      .eq('id', groupId);
    
    if (error) handleSupabaseError(error);
  }
};

// Folders
export const folderService = {
  async getFolders() {
    const { data, error } = await supabase
      .from('folders')
      .select('*')
      .order('name');
    
    if (error) handleSupabaseError(error);
    return data || [];
  },

  async createFolder(folder: Omit<Tables['folders']['Insert'], 'created_by'>) {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('folders')
      .insert({ ...folder, created_by: user.id })
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async updateFolder(folderId: string, updates: Partial<Tables['folders']['Update']>) {
    const { data, error } = await supabase
      .from('folders')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', folderId)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async deleteFolder(folderId: string) {
    const { error } = await supabase
      .from('folders')
      .delete()
      .eq('id', folderId);
    
    if (error) handleSupabaseError(error);
  }
};

// Kanban Columns
export const columnService = {
  async getColumns() {
    const { data, error } = await supabase
      .from('kanban_columns')
      .select('*')
      .order('position');
    
    if (error) handleSupabaseError(error);
    return data || [];
  },

  async createColumn(column: Omit<Tables['kanban_columns']['Insert'], 'created_by'>) {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('kanban_columns')
      .insert({ ...column, created_by: user.id })
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async updateColumn(columnId: string, updates: Partial<Tables['kanban_columns']['Update']>) {
    const { data, error } = await supabase
      .from('kanban_columns')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', columnId)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async deleteColumn(columnId: string) {
    const { error } = await supabase
      .from('kanban_columns')
      .delete()
      .eq('id', columnId);
    
    if (error) handleSupabaseError(error);
  }
};

// Task Comments
export const commentService = {
  async addComment(comment: Tables['task_comments']['Insert']) {
    const { data, error } = await supabase
      .from('task_comments')
      .insert(comment)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async updateComment(commentId: string, content: string) {
    const { data, error } = await supabase
      .from('task_comments')
      .update({ 
        content, 
        edited: true, 
        updated_at: new Date().toISOString() 
      })
      .eq('id', commentId)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async deleteComment(commentId: string) {
    const { error } = await supabase
      .from('task_comments')
      .delete()
      .eq('id', commentId);
    
    if (error) handleSupabaseError(error);
  }
};

// Task History
export const historyService = {
  async addHistoryEntry(entry: Tables['task_history']['Insert']) {
    const { data, error } = await supabase
      .from('task_history')
      .insert(entry)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  }
};

// Skillset Groups
export const skillsetService = {
  async getSkillsetGroups() {
    const { data, error } = await supabase
      .from('skillset_groups')
      .select('*')
      .order('name');
    
    if (error) handleSupabaseError(error);
    return data || [];
  },

  async createSkillsetGroup(skillset: Omit<Tables['skillset_groups']['Insert'], 'created_by'>) {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('skillset_groups')
      .insert({ ...skillset, created_by: user.id })
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async updateSkillsetGroup(skillsetId: string, updates: Partial<Tables['skillset_groups']['Update']>) {
    const { data, error } = await supabase
      .from('skillset_groups')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', skillsetId)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async deleteSkillsetGroup(skillsetId: string) {
    const { error } = await supabase
      .from('skillset_groups')
      .delete()
      .eq('id', skillsetId);
    
    if (error) handleSupabaseError(error);
  }
};

// User Capacities
export const capacityService = {
  async getUserCapacities() {
    const { data, error } = await supabase
      .from('user_capacities')
      .select('*')
      .order('effective_from');
    
    if (error) handleSupabaseError(error);
    return data || [];
  },

  async addUserCapacity(capacity: Tables['user_capacities']['Insert']) {
    const { data, error } = await supabase
      .from('user_capacities')
      .insert(capacity)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async updateUserCapacity(capacityId: string, updates: Partial<Tables['user_capacities']['Update']>) {
    const { data, error } = await supabase
      .from('user_capacities')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', capacityId)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async deleteUserCapacity(capacityId: string) {
    const { error } = await supabase
      .from('user_capacities')
      .delete()
      .eq('id', capacityId);
    
    if (error) handleSupabaseError(error);
  }
};

// Task Efforts
export const effortService = {
  async getTaskEfforts() {
    const { data, error } = await supabase
      .from('task_efforts')
      .select('*');
    
    if (error) handleSupabaseError(error);
    return data || [];
  },

  async addTaskEffort(effort: Tables['task_efforts']['Insert']) {
    const { data, error } = await supabase
      .from('task_efforts')
      .insert(effort)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async updateTaskEffort(effortId: string, updates: Partial<Tables['task_efforts']['Update']>) {
    const { data, error } = await supabase
      .from('task_efforts')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', effortId)
      .select()
      .single();
    
    if (error) handleSupabaseError(error);
    return data;
  },

  async deleteTaskEffort(effortId: string) {
    const { error } = await supabase
      .from('task_efforts')
      .delete()
      .eq('id', effortId);
    
    if (error) handleSupabaseError(error);
  }
};
