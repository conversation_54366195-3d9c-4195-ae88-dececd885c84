-- Step 3: Insert default data manually
-- Run this AFTER creating the admin user successfully

-- Function to insert default data
CREATE OR REPLACE FUNCTION public.insert_default_data_manual()
RETURNS void AS $$
DECLARE
  admin_user_id UUID;
BEGIN
  -- Get the admin user ID
  SELECT id INTO admin_user_id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1;
  
  -- Only proceed if admin user exists
  IF admin_user_id IS NOT NULL THEN
    RAISE NOTICE 'Found admin user: %', admin_user_id;
    
    -- Insert default user groups with proper UUIDs
    INSERT INTO user_groups (name, color, created_by) VALUES
      ('Developers', 'bg-blue-500', admin_user_id),
      ('Project Managers', 'bg-green-500', admin_user_id),
      ('Campaign Managers', 'bg-purple-500', admin_user_id),
      ('Team Leaders', 'bg-red-500', admin_user_id)
    ON CONFLICT DO NOTHING;
    
    RAISE NOTICE 'Inserted user groups';

    -- Insert default kanban columns with proper UUIDs
    INSERT INTO kanban_columns (title, color, position, created_by) VALUES
      ('To Do', 'bg-gray-100', 0, admin_user_id),
      ('In Progress', 'bg-blue-50', 1, admin_user_id),
      ('Review', 'bg-yellow-50', 2, admin_user_id),
      ('Done', 'bg-green-50', 3, admin_user_id)
    ON CONFLICT DO NOTHING;
    
    RAISE NOTICE 'Inserted kanban columns';
  ELSE
    RAISE NOTICE 'Admin user not found';
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Call the function to insert default data
SELECT public.insert_default_data_manual();
