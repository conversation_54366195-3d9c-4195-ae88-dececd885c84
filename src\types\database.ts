export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      user_profiles: {
        Row: {
          id: string
          email: string
          name: string
          avatar_url: string | null
          role: 'admin' | 'user'
          group_id: string | null
          skillset_ids: string[]
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          name: string
          avatar_url?: string | null
          role?: 'admin' | 'user'
          group_id?: string | null
          skillset_ids?: string[]
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string
          avatar_url?: string | null
          role?: 'admin' | 'user'
          group_id?: string | null
          skillset_ids?: string[]
          created_at?: string
          updated_at?: string
        }
      }
      user_groups: {
        Row: {
          id: string
          name: string
          color: string
          created_at: string
          updated_at: string
          created_by: string
        }
        Insert: {
          id?: string
          name: string
          color: string
          created_at?: string
          updated_at?: string
          created_by: string
        }
        Update: {
          id?: string
          name?: string
          color?: string
          created_at?: string
          updated_at?: string
          created_by?: string
        }
      }
      projects: {
        Row: {
          id: string
          name: string
          description: string
          color: string
          start_date: string | null
          end_date: string | null
          folder_id: string | null
          effort: Json | null
          created_at: string
          updated_at: string
          created_by: string
        }
        Insert: {
          id?: string
          name: string
          description: string
          color: string
          start_date?: string | null
          end_date?: string | null
          folder_id?: string | null
          effort?: Json | null
          created_at?: string
          updated_at?: string
          created_by: string
        }
        Update: {
          id?: string
          name?: string
          description?: string
          color?: string
          start_date?: string | null
          end_date?: string | null
          folder_id?: string | null
          effort?: Json | null
          created_at?: string
          updated_at?: string
          created_by?: string
        }
      }
      tasks: {
        Row: {
          id: string
          title: string
          description: string
          status: 'todo' | 'in-progress' | 'review' | 'done'
          priority: 'low' | 'medium' | 'high'
          assigned_user_id: string | null
          assigned_users: string[]
          assigned_groups: string[]
          owner_id: string | null
          due_date: string | null
          start_date: string | null
          tags: string[]
          project_id: string | null
          folder_id: string | null
          effort: Json | null
          created_at: string
          updated_at: string
          created_by: string
        }
        Insert: {
          id?: string
          title: string
          description: string
          status?: 'todo' | 'in-progress' | 'review' | 'done'
          priority?: 'low' | 'medium' | 'high'
          assigned_user_id?: string | null
          assigned_users?: string[]
          assigned_groups?: string[]
          owner_id?: string | null
          due_date?: string | null
          start_date?: string | null
          tags?: string[]
          project_id?: string | null
          folder_id?: string | null
          effort?: Json | null
          created_at?: string
          updated_at?: string
          created_by: string
        }
        Update: {
          id?: string
          title?: string
          description?: string
          status?: 'todo' | 'in-progress' | 'review' | 'done'
          priority?: 'low' | 'medium' | 'high'
          assigned_user_id?: string | null
          assigned_users?: string[]
          assigned_groups?: string[]
          owner_id?: string | null
          due_date?: string | null
          start_date?: string | null
          tags?: string[]
          project_id?: string | null
          folder_id?: string | null
          effort?: Json | null
          created_at?: string
          updated_at?: string
          created_by?: string
        }
      }
      task_comments: {
        Row: {
          id: string
          task_id: string
          user_id: string
          content: string
          parent_id: string | null
          edited: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          task_id: string
          user_id: string
          content: string
          parent_id?: string | null
          edited?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          task_id?: string
          user_id?: string
          content?: string
          parent_id?: string | null
          edited?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      task_history: {
        Row: {
          id: string
          task_id: string
          user_id: string
          field: string
          old_value: string
          new_value: string
          created_at: string
        }
        Insert: {
          id?: string
          task_id: string
          user_id: string
          field: string
          old_value: string
          new_value: string
          created_at?: string
        }
        Update: {
          id?: string
          task_id?: string
          user_id?: string
          field?: string
          old_value?: string
          new_value?: string
          created_at?: string
        }
      }
      folders: {
        Row: {
          id: string
          name: string
          parent_id: string | null
          created_at: string
          updated_at: string
          created_by: string
        }
        Insert: {
          id?: string
          name: string
          parent_id?: string | null
          created_at?: string
          updated_at?: string
          created_by: string
        }
        Update: {
          id?: string
          name?: string
          parent_id?: string | null
          created_at?: string
          updated_at?: string
          created_by?: string
        }
      }
      kanban_columns: {
        Row: {
          id: string
          title: string
          color: string
          position: number
          created_at: string
          updated_at: string
          created_by: string
        }
        Insert: {
          id?: string
          title: string
          color: string
          position?: number
          created_at?: string
          updated_at?: string
          created_by: string
        }
        Update: {
          id?: string
          title?: string
          color?: string
          position?: number
          created_at?: string
          updated_at?: string
          created_by?: string
        }
      }
      skillset_groups: {
        Row: {
          id: string
          name: string
          description: string | null
          color: string
          created_at: string
          updated_at: string
          created_by: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          color: string
          created_at?: string
          updated_at?: string
          created_by: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          color?: string
          created_at?: string
          updated_at?: string
          created_by?: string
        }
      }
      user_capacities: {
        Row: {
          id: string
          user_id: string
          daily_hours: number
          weekly_hours: number
          working_days: number[]
          effective_from: string
          effective_to: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          daily_hours: number
          weekly_hours: number
          working_days: number[]
          effective_from: string
          effective_to?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          daily_hours?: number
          weekly_hours?: number
          working_days?: number[]
          effective_from?: string
          effective_to?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      task_efforts: {
        Row: {
          id: string
          task_id: string
          estimated_hours: number
          actual_hours: number | null
          assigned_user_id: string | null
          required_skillsets: string[]
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          task_id: string
          estimated_hours: number
          actual_hours?: number | null
          assigned_user_id?: string | null
          required_skillsets?: string[]
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          task_id?: string
          estimated_hours?: number
          actual_hours?: number | null
          assigned_user_id?: string | null
          required_skillsets?: string[]
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      user_role: 'admin' | 'user'
      task_status: 'todo' | 'in-progress' | 'review' | 'done'
      task_priority: 'low' | 'medium' | 'high'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
