import React, { useState } from 'react';
import { Task, KanbanColumn, Project } from '../types';
import { MoreVertical, Plus, Pencil, Trash2, ChevronRight, ChevronDown, Check } from 'lucide-react';
import TaskForm from './TaskForm';
import KanbanFilters from './KanbanFilters';
import { useSupabaseStore } from '../store/useSupabaseStore';

interface KanbanProps {
  tasks?: Task[];
  onTaskMove?: (taskId: string, newStatus: Task['status']) => void;
}

export default function KanbanBoard({ tasks: propTasks, onTaskMove }: KanbanProps) {
  const { tasks: storeTasks, columns, projects, users, addTask, updateTask, deleteTask, moveTask } = useSupabaseStore();
  const tasks = propTasks || storeTasks;
  const [showTaskForm, setShowTaskForm] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<Task['status']>('todo');
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [selectedProject, setSelectedProject] = useState<string>('all');
  const [expandedTasks, setExpandedTasks] = useState<Set<string>>(new Set());

  // Filter states
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [selectedGroups, setSelectedGroups] = useState<string[]>([]);
  const [selectedOwners, setSelectedOwners] = useState<string[]>([]);
  const [dueDateFilter, setDueDateFilter] = useState<'all' | 'overdue' | 'today' | 'week' | 'month'>('all');

  const handleDragStart = (e: React.DragEvent, taskId: string) => {
    e.dataTransfer.setData('taskId', taskId);
  };

  const handleDrop = async (e: React.DragEvent, status: Task['status']) => {
    const taskId = e.dataTransfer.getData('taskId');
    try {
      if (onTaskMove) {
        onTaskMove(taskId, status);
      } else {
        await moveTask(taskId, status);
      }
    } catch (error) {
      console.error('Failed to move task:', error);
    }
  };

  const handleAddTask = (status: Task['status']) => {
    setSelectedStatus(status);
    setEditingTask(null);
    setShowTaskForm(true);
  };

  const handleEditTask = (task: Task) => {
    setEditingTask(task);
    setShowTaskForm(true);
  };

  const handleDeleteTask = async (taskId: string) => {
    if (confirm('Are you sure you want to delete this task?')) {
      try {
        await deleteTask(taskId);
      } catch (error) {
        console.error('Failed to delete task:', error);
        alert('Failed to delete task. Please try again.');
      }
    }
  };

  const handleTaskSubmit = async (taskData: Omit<Task, 'id'>) => {
    try {
      if (editingTask) {
        await updateTask(editingTask.id, taskData);
      } else {
        await addTask({ ...taskData, status: selectedStatus });
      }
      setShowTaskForm(false);
      setEditingTask(null);
    } catch (error) {
      console.error('Failed to save task:', error);
      alert(`Failed to save task: ${error.message || 'Unknown error'}. Please try again.`);
    }
  };

  const toggleTaskExpansion = (taskId: string) => {
    setExpandedTasks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(taskId)) {
        newSet.delete(taskId);
      } else {
        newSet.add(taskId);
      }
      return newSet;
    });
  };

  // Clear all filters function
  const clearAllFilters = () => {
    setSelectedProject('all');
    setSelectedUsers([]);
    setSelectedGroups([]);
    setSelectedOwners([]);
    setDueDateFilter('all');
  };

  // Comprehensive filtering logic
  const filteredTasks = tasks.filter(task => {
    // Project filter
    if (selectedProject !== 'all' && task.projectId !== selectedProject) {
      return false;
    }

    // Assigned users filter
    if (selectedUsers.length > 0) {
      const hasMatchingUser = selectedUsers.some(userId =>
        task.assignedUsers?.includes(userId) || task.assignedUserId === userId
      );
      if (!hasMatchingUser) return false;
    }

    // User groups filter
    if (selectedGroups.length > 0) {
      const hasMatchingGroup = selectedGroups.some(groupId =>
        task.assignedGroups?.includes(groupId)
      );
      if (!hasMatchingGroup) return false;
    }

    // Owners filter
    if (selectedOwners.length > 0) {
      const hasMatchingOwner = selectedOwners.some(ownerId =>
        task.ownerId === ownerId || task.owner === ownerId
      );
      if (!hasMatchingOwner) return false;
    }

    // Due date filter
    if (dueDateFilter !== 'all' && task.dueDate) {
      const dueDate = new Date(task.dueDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const weekFromNow = new Date(today);
      weekFromNow.setDate(weekFromNow.getDate() + 7);

      const monthFromNow = new Date(today);
      monthFromNow.setMonth(monthFromNow.getMonth() + 1);

      switch (dueDateFilter) {
        case 'overdue':
          if (dueDate >= today) return false;
          break;
        case 'today':
          if (dueDate < today || dueDate >= tomorrow) return false;
          break;
        case 'week':
          if (dueDate < today || dueDate >= weekFromNow) return false;
          break;
        case 'month':
          if (dueDate < today || dueDate >= monthFromNow) return false;
          break;
      }
    }

    return true;
  });

  const getUserName = (userId: string) => {
    const user = users.find(u => u.id === userId);
    return user ? user.name : userId;
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex-shrink-0 p-4 pb-2">
        <KanbanFilters
          selectedProject={selectedProject}
          setSelectedProject={setSelectedProject}
          selectedUsers={selectedUsers}
          setSelectedUsers={setSelectedUsers}
          selectedGroups={selectedGroups}
          setSelectedGroups={setSelectedGroups}
          selectedOwners={selectedOwners}
          setSelectedOwners={setSelectedOwners}
          dueDateFilter={dueDateFilter}
          setDueDateFilter={setDueDateFilter}
          onClearFilters={clearAllFilters}
        />
      </div>

      <div className="flex-1 overflow-x-auto overflow-y-hidden">
        <div className="flex gap-4 h-full p-4 pt-2 min-w-max">
        {columns.map(({ id, title, color }) => (
          <div
            key={id}
            className="flex-shrink-0 w-80 h-full flex flex-col"
            onDragOver={(e) => e.preventDefault()}
            onDrop={(e) => handleDrop(e, id as Task['status'])}
          >
            <div className={`rounded-lg ${color} p-4 h-full flex flex-col`}>
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold">{title}</h3>
                <button 
                  className="p-1 hover:bg-gray-200 rounded"
                  onClick={() => handleAddTask(id as Task['status'])}
                >
                  <Plus className="w-5 h-5" />
                </button>
              </div>
              
              <div className="flex-1 overflow-y-auto space-y-3">
                {filteredTasks
                  .filter((task) => task.status === id)
                  .map((task) => (
                    <div
                      key={task.id}
                      draggable
                      onDragStart={(e) => handleDragStart(e, task.id)}
                      className="bg-white rounded-lg shadow-sm cursor-move hover:shadow-md transition-shadow group"
                    >
                      <div className="p-3">
                        <div className="flex justify-between items-start">
                          <div className="flex items-start gap-2 flex-1">
                            {task.subtasks.length > 0 && (
                              <button
                                onClick={() => toggleTaskExpansion(task.id)}
                                className="p-1 hover:bg-gray-100 rounded mt-1"
                              >
                                {expandedTasks.has(task.id) ? (
                                  <ChevronDown className="w-4 h-4" />
                                ) : (
                                  <ChevronRight className="w-4 h-4" />
                                )}
                              </button>
                            )}
                            <div className="flex-1">
                              <h4 className="font-medium">{task.title}</h4>
                              <p className="text-sm text-gray-600 mt-1">{task.description}</p>
                            </div>
                          </div>
                          <div className="flex gap-1">
                            <button 
                              onClick={() => handleEditTask(task)}
                              className="p-1 hover:bg-gray-100 rounded opacity-0 group-hover:opacity-100 transition-opacity"
                            >
                              <Pencil className="w-4 h-4" />
                            </button>
                            <button 
                              onClick={() => handleDeleteTask(task.id)}
                              className="p-1 hover:bg-red-50 rounded opacity-0 group-hover:opacity-100 transition-opacity text-red-600"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>

                        {task.projectId && (
                          <div className="mt-2">
                            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                              {projects.find(p => p.id === task.projectId)?.name}
                            </span>
                          </div>
                        )}

                        {task.assignedGroups && task.assignedGroups.length > 0 && (
                          <div className="flex gap-1 mt-2">
                            {task.assignedGroups.map((groupId) => (
                              <span
                                key={groupId}
                                className="px-2 py-0.5 bg-purple-100 text-purple-800 rounded text-xs"
                              >
                                {groupId}
                              </span>
                            ))}
                          </div>
                        )}

                        <div className="flex gap-2 mt-2">
                          {task.tags.map((tag) => (
                            <span
                              key={tag}
                              className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>

                        {task.dueDate && (
                          <div className="text-xs text-gray-500 mt-2">
                            Due: {new Date(task.dueDate).toLocaleDateString()}
                          </div>
                        )}
                      </div>

                      {/* Subtasks section */}
                      {expandedTasks.has(task.id) && task.subtasks.length > 0 && (
                        <div className="border-t px-3 py-2 space-y-2">
                          {task.subtasks.map((subtask) => (
                            <div
                              key={subtask.id}
                              className="flex items-center gap-2 text-sm hover:bg-gray-50 p-1 rounded group/subtask"
                            >
                              <button
                                onClick={() => updateSubtask(task.id, subtask.id, { completed: !subtask.completed })}
                                className={`w-4 h-4 rounded border flex items-center justify-center flex-shrink-0 ${
                                  subtask.completed ? 'bg-blue-600 border-blue-600' : 'border-gray-300'
                                }`}
                              >
                                {subtask.completed && <Check className="w-3 h-3 text-white" />}
                              </button>
                              <span className={`flex-1 ${subtask.completed ? 'line-through text-gray-500' : ''}`}>
                                {subtask.title}
                              </span>
                              {subtask.assignedUserId && (
                                <span className="text-xs text-gray-500">
                                  {getUserName(subtask.assignedUserId)}
                                </span>
                              )}
                              <div className="opacity-0 group-hover/subtask:opacity-100 transition-opacity">
                                <button
                                  onClick={() => handleEditTask({ ...task, id: subtask.id })}
                                  className="p-1 hover:bg-gray-100 rounded"
                                >
                                  <Pencil className="w-3 h-3" />
                                </button>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
              </div>
            </div>
          </div>
        ))}
        </div>
      </div>
    </div>

      {showTaskForm && (
        <TaskForm
          onSubmit={handleTaskSubmit}
          onClose={() => {
            setShowTaskForm(false);
            setEditingTask(null);
          }}
          initialData={editingTask}
        />
      )}
    </>
  );
}