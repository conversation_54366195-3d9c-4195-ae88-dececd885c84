import React from 'react';
import { Project } from '../types';
import { Edit2, Trash2 } from 'lucide-react';

interface ProjectListProps {
  projects: Project[];
  onEditProject: (project: Project) => void;
  onDeleteProject: (projectId: string) => void;
}

export default function ProjectList({ projects, onEditProject, onDeleteProject }: ProjectListProps) {
  return (
    <div className="p-4">
      <div className="space-y-2">
        {projects.map((project) => (
          <div
            key={project.id}
            className="flex items-center justify-between p-3 hover:bg-gray-800 rounded-lg"
          >
            <div className="flex items-center gap-3">
              <div className={`w-4 h-4 rounded-full ${project.color}`} />
              <div>
                <h3 className="font-medium">{project.name}</h3>
                <p className="text-sm text-gray-400">{project.description}</p>
              </div>
            </div>
            
            <div className="flex gap-2">
              <button
                onClick={() => onEditProject(project)}
                className="p-1 hover:bg-gray-700 rounded"
              >
                <Edit2 className="w-4 h-4" />
              </button>
              <button
                onClick={() => onDeleteProject(project.id)}
                className="p-1 hover:bg-gray-700 rounded text-red-400"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}