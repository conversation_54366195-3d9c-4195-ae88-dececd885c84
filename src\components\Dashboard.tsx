import React, { useMemo } from 'react';
import { useStore } from '../store/useStore';
import { CheckSquare, Clock, Users, AlertTriangle, TrendingUp, Calendar, Target, Award } from 'lucide-react';
import { differenceInMinutes, isAfter, isBefore, addDays, startOfDay, endOfDay } from 'date-fns';

export default function Dashboard() {
  const { tasks, projects, users, columns } = useStore();

  // Calculate in-progress time for a task
  const calculateInProgressHours = (task: any): number => {
    if (!task.durations || task.durations.length === 0) return 0;

    const inProgressColumn = columns.find(col => 
      col.title.toLowerCase().includes('progress') || 
      col.id === 'in-progress' ||
      col.title.toLowerCase() === 'in progress'
    );
    
    if (!inProgressColumn) return 0;

    let totalMinutes = 0;
    task.durations.forEach((duration: any) => {
      if (duration.status === inProgressColumn.id) {
        const start = new Date(duration.startTime);
        const end = duration.endTime ? new Date(duration.endTime) : 
          (task.status === inProgressColumn.id ? new Date() : start);
        
        totalMinutes += differenceInMinutes(end, start);
      }
    });

    return Math.round((totalMinutes / 60) * 10) / 10;
  };

  // Dashboard statistics
  const dashboardStats = useMemo(() => {
    const today = new Date();
    const todayStart = startOfDay(today);
    const todayEnd = endOfDay(today);
    const weekFromNow = addDays(today, 7);

    // Task statistics
    const totalTasks = tasks.length;
    const completedTasks = tasks.filter(task => task.status === 'done').length;
    const inProgressTasks = tasks.filter(task => task.status === 'in-progress').length;
    const todoTasks = tasks.filter(task => task.status === 'todo').length;

    // Due date analysis
    const overdueTasks = tasks.filter(task => 
      task.dueDate && isBefore(new Date(task.dueDate), todayStart) && task.status !== 'done'
    ).length;
    
    const dueTodayTasks = tasks.filter(task => 
      task.dueDate && 
      isAfter(new Date(task.dueDate), todayStart) && 
      isBefore(new Date(task.dueDate), todayEnd) &&
      task.status !== 'done'
    ).length;

    const dueThisWeekTasks = tasks.filter(task => 
      task.dueDate && 
      isAfter(new Date(task.dueDate), todayEnd) && 
      isBefore(new Date(task.dueDate), weekFromNow) &&
      task.status !== 'done'
    ).length;

    // Effort analysis
    const tasksWithEffort = tasks.filter(task => task.effort && task.effort.estimatedHours > 0);
    const totalEstimatedHours = tasksWithEffort.reduce((sum, task) => sum + (task.effort?.estimatedHours || 0), 0);
    const totalActualHours = tasksWithEffort.reduce((sum, task) => sum + calculateInProgressHours(task), 0);
    const effortVariance = totalActualHours - totalEstimatedHours;

    // Project statistics
    const activeProjects = projects.length;
    const projectsWithTasks = projects.filter(project => 
      tasks.some(task => task.projectId === project.id)
    ).length;

    // Team statistics
    const assignedTasks = tasks.filter(task => 
      task.assignedUsers?.length > 0 || task.assignedUserId
    ).length;
    const unassignedTasks = totalTasks - assignedTasks;

    // Completion rate
    const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

    return {
      totalTasks,
      completedTasks,
      inProgressTasks,
      todoTasks,
      overdueTasks,
      dueTodayTasks,
      dueThisWeekTasks,
      totalEstimatedHours,
      totalActualHours,
      effortVariance,
      activeProjects,
      projectsWithTasks,
      assignedTasks,
      unassignedTasks,
      completionRate
    };
  }, [tasks, projects, columns]);

  // Project overview data
  const projectOverview = useMemo(() => {
    return projects.map(project => {
      const projectTasks = tasks.filter(task => task.projectId === project.id);
      const completedProjectTasks = projectTasks.filter(task => task.status === 'done').length;
      const completionRate = projectTasks.length > 0 ? (completedProjectTasks / projectTasks.length) * 100 : 0;
      
      const overdueTasks = projectTasks.filter(task => 
        task.dueDate && isBefore(new Date(task.dueDate), new Date()) && task.status !== 'done'
      ).length;

      return {
        id: project.id,
        name: project.name,
        color: project.color,
        totalTasks: projectTasks.length,
        completedTasks: completedProjectTasks,
        completionRate,
        overdueTasks
      };
    }).filter(project => project.totalTasks > 0);
  }, [projects, tasks]);

  const formatHours = (hours: number) => `${hours.toFixed(1)}h`;

  return (
    <div className="p-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-2">Overview of your projects and tasks</p>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Tasks</p>
                <p className="text-2xl font-bold text-gray-900">{dashboardStats.totalTasks}</p>
                <p className="text-sm text-gray-500 mt-1">
                  {dashboardStats.completionRate.toFixed(1)}% completed
                </p>
              </div>
              <CheckSquare className="w-8 h-8 text-blue-600" />
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">In Progress</p>
                <p className="text-2xl font-bold text-orange-600">{dashboardStats.inProgressTasks}</p>
                <p className="text-sm text-gray-500 mt-1">
                  {dashboardStats.todoTasks} to do
                </p>
              </div>
              <Clock className="w-8 h-8 text-orange-600" />
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Overdue Tasks</p>
                <p className="text-2xl font-bold text-red-600">{dashboardStats.overdueTasks}</p>
                <p className="text-sm text-gray-500 mt-1">
                  {dashboardStats.dueTodayTasks} due today
                </p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Projects</p>
                <p className="text-2xl font-bold text-green-600">{dashboardStats.projectsWithTasks}</p>
                <p className="text-sm text-gray-500 mt-1">
                  of {dashboardStats.activeProjects} total
                </p>
              </div>
              <Target className="w-8 h-8 text-green-600" />
            </div>
          </div>
        </div>

        {/* Effort Overview */}
        {dashboardStats.totalEstimatedHours > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white p-6 rounded-lg shadow border">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Estimated Effort</h3>
                <Target className="w-5 h-5 text-blue-600" />
              </div>
              <p className="text-2xl font-bold text-blue-600">{formatHours(dashboardStats.totalEstimatedHours)}</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow border">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Actual Effort</h3>
                <Clock className="w-5 h-5 text-purple-600" />
              </div>
              <p className="text-2xl font-bold text-purple-600">{formatHours(dashboardStats.totalActualHours)}</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow border">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Effort Variance</h3>
                <TrendingUp className="w-5 h-5 text-gray-600" />
              </div>
              <p className={`text-2xl font-bold ${
                dashboardStats.effortVariance >= 0 ? 'text-red-600' : 'text-green-600'
              }`}>
                {dashboardStats.effortVariance >= 0 ? '+' : ''}{formatHours(dashboardStats.effortVariance)}
              </p>
            </div>
          </div>
        )}

        {/* Project Overview Table */}
        <div className="bg-white rounded-lg shadow border">
          <div className="p-6 border-b">
            <h2 className="text-xl font-semibold">Project Overview</h2>
          </div>
          <div className="p-6">
            {projectOverview.length === 0 ? (
              <p className="text-gray-500 text-center py-8">No projects with tasks found</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-4">Project</th>
                      <th className="text-center py-3 px-4">Total Tasks</th>
                      <th className="text-center py-3 px-4">Completed</th>
                      <th className="text-center py-3 px-4">Progress</th>
                      <th className="text-center py-3 px-4">Overdue</th>
                    </tr>
                  </thead>
                  <tbody>
                    {projectOverview.map((project) => (
                      <tr key={project.id} className="border-b hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <div className="flex items-center gap-3">
                            <div 
                              className="w-4 h-4 rounded"
                              style={{ backgroundColor: project.color }}
                            ></div>
                            <span className="font-medium">{project.name}</span>
                          </div>
                        </td>
                        <td className="text-center py-3 px-4">{project.totalTasks}</td>
                        <td className="text-center py-3 px-4">{project.completedTasks}</td>
                        <td className="py-3 px-4">
                          <div className="flex items-center justify-center">
                            <div className="w-24 bg-gray-200 rounded-full h-2 mr-2">
                              <div 
                                className="bg-green-600 h-2 rounded-full" 
                                style={{ width: `${project.completionRate}%` }}
                              ></div>
                            </div>
                            <span className="text-sm text-gray-600 min-w-[3rem]">
                              {project.completionRate.toFixed(0)}%
                            </span>
                          </div>
                        </td>
                        <td className="text-center py-3 px-4">
                          {project.overdueTasks > 0 ? (
                            <span className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-sm">
                              {project.overdueTasks}
                            </span>
                          ) : (
                            <span className="text-gray-400">0</span>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
