import React, { useState, useMemo } from 'react';
import { useStore } from '../store/useStore';
import { BarChart3, TrendingUp, Users, Clock, Target, AlertTriangle, Award, Pie<PERSON>hart, <PERSON>older<PERSON><PERSON>ban, <PERSON><PERSON>, CheckSquare } from 'lucide-react';
import { differenceInMinutes } from 'date-fns';

export default function Analytics() {
  const { tasks, users, projects, skillsetGroups, columns } = useStore();
  const [selectedTimeframe, setSelectedTimeframe] = useState<'week' | 'month' | 'quarter' | 'all'>('month');
  const [selectedReport, setSelectedReport] = useState<'overview' | 'estimation' | 'team' | 'projects' | 'skillsets'>('overview');

  // Calculate in-progress time for a task
  const calculateInProgressHours = (task: any): number => {
    if (!task.durations || task.durations.length === 0) return 0;

    const inProgressColumn = columns.find(col => 
      col.title.toLowerCase().includes('progress') || 
      col.id === 'in-progress' ||
      col.title.toLowerCase() === 'in progress'
    );
    
    if (!inProgressColumn) return 0;

    let totalMinutes = 0;
    task.durations.forEach((duration: any) => {
      if (duration.status === inProgressColumn.id) {
        const start = new Date(duration.startTime);
        const end = duration.endTime ? new Date(duration.endTime) : 
          (task.status === inProgressColumn.id ? new Date() : start);
        
        totalMinutes += differenceInMinutes(end, start);
      }
    });

    return Math.round((totalMinutes / 60) * 10) / 10;
  };

  // Filter tasks based on timeframe
  const filteredTasks = useMemo(() => {
    const now = new Date();
    const cutoffDate = new Date();
    
    switch (selectedTimeframe) {
      case 'week':
        cutoffDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        cutoffDate.setMonth(now.getMonth() - 1);
        break;
      case 'quarter':
        cutoffDate.setMonth(now.getMonth() - 3);
        break;
      default:
        return tasks;
    }

    return tasks.filter(task => {
      const taskDate = task.dueDate ? new Date(task.dueDate) : new Date();
      return taskDate >= cutoffDate;
    });
  }, [tasks, selectedTimeframe]);

  // Calculate analytics data
  const analyticsData = useMemo(() => {
    const tasksWithEffort = filteredTasks.filter(task => task.effort && task.effort.estimatedHours > 0);
    
    // Estimation Accuracy
    const estimationData = tasksWithEffort.map(task => {
      const estimated = task.effort?.estimatedHours || 0;
      const actual = calculateInProgressHours(task);
      const variance = actual - estimated;
      const accuracy = estimated > 0 ? ((estimated - Math.abs(variance)) / estimated) * 100 : 0;
      
      return {
        taskId: task.id,
        title: task.title,
        estimated,
        actual,
        variance,
        accuracy: Math.max(0, accuracy),
        assignedUserId: task.effort?.assignedUserId,
        projectId: task.projectId,
        requiredSkillsets: task.effort?.requiredSkillsets || [],
        status: task.status
      };
    });

    // Overall metrics
    const totalEstimated = estimationData.reduce((sum, item) => sum + item.estimated, 0);
    const totalActual = estimationData.reduce((sum, item) => sum + item.actual, 0);
    const overallVariance = totalActual - totalEstimated;
    const overallAccuracy = totalEstimated > 0 ? ((totalEstimated - Math.abs(overallVariance)) / totalEstimated) * 100 : 0;

    // Team performance
    const teamPerformance = users.map(user => {
      const userTasks = estimationData.filter(task => task.assignedUserId === user.id);
      const userEstimated = userTasks.reduce((sum, task) => sum + task.estimated, 0);
      const userActual = userTasks.reduce((sum, task) => sum + task.actual, 0);
      const userVariance = userActual - userEstimated;
      const userAccuracy = userEstimated > 0 ? ((userEstimated - Math.abs(userVariance)) / userEstimated) * 100 : 0;

      return {
        userId: user.id,
        name: user.name,
        tasksCount: userTasks.length,
        estimated: userEstimated,
        actual: userActual,
        variance: userVariance,
        accuracy: Math.max(0, userAccuracy),
        completedTasks: userTasks.filter(task => task.status === 'done').length
      };
    }).filter(user => user.tasksCount > 0);

    // Project performance
    const projectPerformance = projects.map(project => {
      const projectTasks = estimationData.filter(task => task.projectId === project.id);
      const projectEstimated = projectTasks.reduce((sum, task) => sum + task.estimated, 0);
      const projectActual = projectTasks.reduce((sum, task) => sum + task.actual, 0);
      const projectVariance = projectActual - projectEstimated;
      const projectAccuracy = projectEstimated > 0 ? ((projectEstimated - Math.abs(projectVariance)) / projectEstimated) * 100 : 0;

      return {
        projectId: project.id,
        name: project.name,
        tasksCount: projectTasks.length,
        estimated: projectEstimated,
        actual: projectActual,
        variance: projectVariance,
        accuracy: Math.max(0, projectAccuracy),
        completedTasks: projectTasks.filter(task => task.status === 'done').length
      };
    }).filter(project => project.tasksCount > 0);

    // Skillset performance
    const skillsetPerformance = skillsetGroups.map(skillset => {
      const skillsetTasks = estimationData.filter(task => 
        task.requiredSkillsets.includes(skillset.id)
      );
      const skillsetEstimated = skillsetTasks.reduce((sum, task) => sum + task.estimated, 0);
      const skillsetActual = skillsetTasks.reduce((sum, task) => sum + task.actual, 0);
      const skillsetVariance = skillsetActual - skillsetEstimated;
      const skillsetAccuracy = skillsetEstimated > 0 ? ((skillsetEstimated - Math.abs(skillsetVariance)) / skillsetEstimated) * 100 : 0;

      return {
        skillsetId: skillset.id,
        name: skillset.name,
        tasksCount: skillsetTasks.length,
        estimated: skillsetEstimated,
        actual: skillsetActual,
        variance: skillsetVariance,
        accuracy: Math.max(0, skillsetAccuracy),
        completedTasks: skillsetTasks.filter(task => task.status === 'done').length
      };
    }).filter(skillset => skillset.tasksCount > 0);

    return {
      totalTasks: tasksWithEffort.length,
      totalEstimated,
      totalActual,
      overallVariance,
      overallAccuracy,
      estimationData,
      teamPerformance,
      projectPerformance,
      skillsetPerformance
    };
  }, [filteredTasks, users, projects, skillsetGroups, columns]);

  const formatHours = (hours: number) => `${hours.toFixed(1)}h`;
  const formatVariance = (variance: number) => {
    const sign = variance >= 0 ? '+' : '';
    return `${sign}${variance.toFixed(1)}h`;
  };

  const getVarianceColor = (variance: number) => {
    if (Math.abs(variance) <= 1) return 'text-green-600';
    if (Math.abs(variance) <= 3) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getAccuracyColor = (accuracy: number) => {
    if (accuracy >= 80) return 'text-green-600';
    if (accuracy >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const renderOverview = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div className="bg-white p-6 rounded-lg shadow border">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">Total Tasks</p>
            <p className="text-2xl font-bold text-gray-900">{analyticsData.totalTasks}</p>
          </div>
          <CheckSquare className="w-8 h-8 text-blue-600" />
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow border">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">Estimated Hours</p>
            <p className="text-2xl font-bold text-gray-900">{formatHours(analyticsData.totalEstimated)}</p>
          </div>
          <Target className="w-8 h-8 text-green-600" />
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow border">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">Actual Hours</p>
            <p className="text-2xl font-bold text-gray-900">{formatHours(analyticsData.totalActual)}</p>
          </div>
          <Clock className="w-8 h-8 text-purple-600" />
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow border">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">Overall Accuracy</p>
            <p className={`text-2xl font-bold ${getAccuracyColor(analyticsData.overallAccuracy)}`}>
              {analyticsData.overallAccuracy.toFixed(1)}%
            </p>
          </div>
          <Award className="w-8 h-8 text-yellow-600" />
        </div>
      </div>
    </div>
  );

  return (
    <div className="p-8">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <BarChart3 className="w-8 h-8" />
              Analytics & Reports
            </h1>
            <p className="text-gray-600 mt-2">Estimated vs Actual Effort Analysis</p>
          </div>

          <div className="flex gap-4">
            <select
              value={selectedTimeframe}
              onChange={(e) => setSelectedTimeframe(e.target.value as any)}
              className="border rounded-lg px-3 py-2"
            >
              <option value="week">Last Week</option>
              <option value="month">Last Month</option>
              <option value="quarter">Last Quarter</option>
              <option value="all">All Time</option>
            </select>

            <select
              value={selectedReport}
              onChange={(e) => setSelectedReport(e.target.value as any)}
              className="border rounded-lg px-3 py-2"
            >
              <option value="overview">Overview</option>
              <option value="estimation">Estimation Analysis</option>
              <option value="team">Team Performance</option>
              <option value="projects">Project Analysis</option>
              <option value="skillsets">Skillset Analysis</option>
            </select>
          </div>
        </div>

        {selectedReport === 'overview' && renderOverview()}

        {selectedReport === 'estimation' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow border">
              <div className="p-6 border-b">
                <h2 className="text-xl font-semibold flex items-center gap-2">
                  <TrendingUp className="w-5 h-5" />
                  Estimation vs Actual Analysis
                </h2>
              </div>
              <div className="p-6">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4">Task</th>
                        <th className="text-right py-3 px-4">Estimated</th>
                        <th className="text-right py-3 px-4">Actual</th>
                        <th className="text-right py-3 px-4">Variance</th>
                        <th className="text-right py-3 px-4">Accuracy</th>
                        <th className="text-left py-3 px-4">Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {analyticsData.estimationData
                        .sort((a, b) => Math.abs(b.variance) - Math.abs(a.variance))
                        .slice(0, 20)
                        .map((task) => (
                        <tr key={task.taskId} className="border-b hover:bg-gray-50">
                          <td className="py-3 px-4">
                            <div className="font-medium">{task.title}</div>
                          </td>
                          <td className="text-right py-3 px-4">{formatHours(task.estimated)}</td>
                          <td className="text-right py-3 px-4">{formatHours(task.actual)}</td>
                          <td className={`text-right py-3 px-4 font-medium ${getVarianceColor(task.variance)}`}>
                            {formatVariance(task.variance)}
                          </td>
                          <td className={`text-right py-3 px-4 font-medium ${getAccuracyColor(task.accuracy)}`}>
                            {task.accuracy.toFixed(1)}%
                          </td>
                          <td className="py-3 px-4">
                            <span className={`px-2 py-1 rounded text-xs ${
                              task.status === 'done' ? 'bg-green-100 text-green-800' :
                              task.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                              task.status === 'review' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {task.status}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        )}

        {selectedReport === 'team' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow border">
              <div className="p-6 border-b">
                <h2 className="text-xl font-semibold flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  Team Performance Analysis
                </h2>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {analyticsData.teamPerformance
                    .sort((a, b) => b.accuracy - a.accuracy)
                    .map((member) => (
                    <div key={member.userId} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-semibold">{member.name}</h3>
                        <span className={`px-2 py-1 rounded text-xs font-medium ${getAccuracyColor(member.accuracy)} bg-opacity-10`}>
                          {member.accuracy.toFixed(1)}%
                        </span>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Tasks:</span>
                          <span>{member.tasksCount} ({member.completedTasks} done)</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Estimated:</span>
                          <span>{formatHours(member.estimated)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Actual:</span>
                          <span>{formatHours(member.actual)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Variance:</span>
                          <span className={getVarianceColor(member.variance)}>
                            {formatVariance(member.variance)}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {selectedReport === 'projects' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow border">
              <div className="p-6 border-b">
                <h2 className="text-xl font-semibold flex items-center gap-2">
                  <FolderKanban className="w-5 h-5" />
                  Project Performance Analysis
                </h2>
              </div>
              <div className="p-6">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4">Project</th>
                        <th className="text-center py-3 px-4">Tasks</th>
                        <th className="text-right py-3 px-4">Estimated</th>
                        <th className="text-right py-3 px-4">Actual</th>
                        <th className="text-right py-3 px-4">Variance</th>
                        <th className="text-right py-3 px-4">Accuracy</th>
                        <th className="text-center py-3 px-4">Completion</th>
                      </tr>
                    </thead>
                    <tbody>
                      {analyticsData.projectPerformance
                        .sort((a, b) => b.accuracy - a.accuracy)
                        .map((project) => (
                        <tr key={project.projectId} className="border-b hover:bg-gray-50">
                          <td className="py-3 px-4">
                            <div className="font-medium">{project.name}</div>
                          </td>
                          <td className="text-center py-3 px-4">{project.tasksCount}</td>
                          <td className="text-right py-3 px-4">{formatHours(project.estimated)}</td>
                          <td className="text-right py-3 px-4">{formatHours(project.actual)}</td>
                          <td className={`text-right py-3 px-4 font-medium ${getVarianceColor(project.variance)}`}>
                            {formatVariance(project.variance)}
                          </td>
                          <td className={`text-right py-3 px-4 font-medium ${getAccuracyColor(project.accuracy)}`}>
                            {project.accuracy.toFixed(1)}%
                          </td>
                          <td className="text-center py-3 px-4">
                            <div className="flex items-center justify-center">
                              <div className="w-16 bg-gray-200 rounded-full h-2">
                                <div
                                  className="bg-green-600 h-2 rounded-full"
                                  style={{ width: `${(project.completedTasks / project.tasksCount) * 100}%` }}
                                ></div>
                              </div>
                              <span className="ml-2 text-sm text-gray-600">
                                {Math.round((project.completedTasks / project.tasksCount) * 100)}%
                              </span>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        )}

        {selectedReport === 'skillsets' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow border">
              <div className="p-6 border-b">
                <h2 className="text-xl font-semibold flex items-center gap-2">
                  <Palette className="w-5 h-5" />
                  Skillset Performance Analysis
                </h2>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {analyticsData.skillsetPerformance
                    .sort((a, b) => b.tasksCount - a.tasksCount)
                    .map((skillset) => (
                    <div key={skillset.skillsetId} className="border rounded-lg p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="font-semibold text-lg">{skillset.name}</h3>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getAccuracyColor(skillset.accuracy)} bg-opacity-10`}>
                          {skillset.accuracy.toFixed(1)}% accuracy
                        </span>
                      </div>

                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div className="text-center p-3 bg-gray-50 rounded">
                          <div className="text-2xl font-bold text-gray-900">{skillset.tasksCount}</div>
                          <div className="text-sm text-gray-600">Total Tasks</div>
                        </div>
                        <div className="text-center p-3 bg-gray-50 rounded">
                          <div className="text-2xl font-bold text-green-600">{skillset.completedTasks}</div>
                          <div className="text-sm text-gray-600">Completed</div>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Estimated Hours:</span>
                          <span className="font-medium">{formatHours(skillset.estimated)}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Actual Hours:</span>
                          <span className="font-medium">{formatHours(skillset.actual)}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Variance:</span>
                          <span className={`font-medium ${getVarianceColor(skillset.variance)}`}>
                            {formatVariance(skillset.variance)}
                          </span>
                        </div>

                        <div className="pt-2">
                          <div className="flex justify-between text-sm text-gray-600 mb-1">
                            <span>Estimation Accuracy</span>
                            <span>{skillset.accuracy.toFixed(1)}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${
                                skillset.accuracy >= 80 ? 'bg-green-600' :
                                skillset.accuracy >= 60 ? 'bg-yellow-600' : 'bg-red-600'
                              }`}
                              style={{ width: `${skillset.accuracy}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
