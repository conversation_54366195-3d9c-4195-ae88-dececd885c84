-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON>reate custom types
CREATE TYPE user_role AS ENUM ('admin', 'user');
CREATE TYPE task_status AS ENUM ('todo', 'in-progress', 'review', 'done');
CREATE TYPE task_priority AS ENUM ('low', 'medium', 'high');

-- User profiles table (extends auth.users)
CREATE TABLE user_profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  name TEXT NOT NULL,
  avatar_url TEXT,
  role user_role DEFAULT 'user',
  group_id UUID,
  skillset_ids TEXT[] DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- User groups table
CREATE TABLE user_groups (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  color TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL
);

-- Folders table
CREATE TABLE folders (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  parent_id UUID REFERENCES folders(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL
);

-- Projects table
CREATE TABLE projects (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  color TEXT NOT NULL,
  start_date DATE,
  end_date DATE,
  folder_id UUID REFERENCES folders(id) ON DELETE SET NULL,
  effort JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL
);

-- Kanban columns table
CREATE TABLE kanban_columns (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  title TEXT NOT NULL,
  color TEXT NOT NULL,
  position INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL
);

-- Skillset groups table
CREATE TABLE skillset_groups (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  color TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL
);

-- Tasks table
CREATE TABLE tasks (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  status task_status DEFAULT 'todo',
  priority task_priority DEFAULT 'medium',
  assigned_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  assigned_users TEXT[] DEFAULT '{}',
  assigned_groups TEXT[] DEFAULT '{}',
  owner_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  due_date DATE,
  start_date DATE,
  tags TEXT[] DEFAULT '{}',
  project_id UUID REFERENCES projects(id) ON DELETE SET NULL,
  folder_id UUID REFERENCES folders(id) ON DELETE SET NULL,
  effort JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL
);

-- Task comments table
CREATE TABLE task_comments (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  content TEXT NOT NULL,
  parent_id UUID REFERENCES task_comments(id) ON DELETE CASCADE,
  edited BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Task history table
CREATE TABLE task_history (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  field TEXT NOT NULL,
  old_value TEXT NOT NULL,
  new_value TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User capacities table
CREATE TABLE user_capacities (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  daily_hours DECIMAL(4,2) NOT NULL,
  weekly_hours DECIMAL(4,2) NOT NULL,
  working_days INTEGER[] NOT NULL,
  effective_from DATE NOT NULL,
  effective_to DATE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Task efforts table
CREATE TABLE task_efforts (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  task_id UUID REFERENCES tasks(id) ON DELETE CASCADE NOT NULL,
  estimated_hours DECIMAL(6,2) NOT NULL,
  actual_hours DECIMAL(6,2),
  assigned_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  required_skillsets TEXT[] DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add foreign key constraint for user_profiles.group_id
ALTER TABLE user_profiles 
ADD CONSTRAINT fk_user_profiles_group_id 
FOREIGN KEY (group_id) REFERENCES user_groups(id) ON DELETE SET NULL;

-- Create indexes for better performance
CREATE INDEX idx_tasks_assigned_user_id ON tasks(assigned_user_id);
CREATE INDEX idx_tasks_project_id ON tasks(project_id);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_created_at ON tasks(created_at);
CREATE INDEX idx_task_comments_task_id ON task_comments(task_id);
CREATE INDEX idx_task_history_task_id ON task_history(task_id);
CREATE INDEX idx_user_capacities_user_id ON user_capacities(user_id);
CREATE INDEX idx_task_efforts_task_id ON task_efforts(task_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers to all tables
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_groups_updated_at BEFORE UPDATE ON user_groups FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_folders_updated_at BEFORE UPDATE ON folders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_kanban_columns_updated_at BEFORE UPDATE ON kanban_columns FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_skillset_groups_updated_at BEFORE UPDATE ON skillset_groups FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_task_comments_updated_at BEFORE UPDATE ON task_comments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_capacities_updated_at BEFORE UPDATE ON user_capacities FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_task_efforts_updated_at BEFORE UPDATE ON task_efforts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security on all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE folders ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE kanban_columns ENABLE ROW LEVEL SECURITY;
ALTER TABLE skillset_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_capacities ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_efforts ENABLE ROW LEVEL SECURITY;

-- User profiles policies
CREATE POLICY "Users can view all profiles" ON user_profiles FOR SELECT USING (true);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Admins can update any profile" ON user_profiles FOR UPDATE USING (
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- User groups policies
CREATE POLICY "Users can view all user groups" ON user_groups FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create user groups" ON user_groups FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Creators and admins can update user groups" ON user_groups FOR UPDATE USING (
  created_by = auth.uid() OR
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);
CREATE POLICY "Creators and admins can delete user groups" ON user_groups FOR DELETE USING (
  created_by = auth.uid() OR
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Folders policies
CREATE POLICY "Users can view all folders" ON folders FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create folders" ON folders FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Creators and admins can update folders" ON folders FOR UPDATE USING (
  created_by = auth.uid() OR
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);
CREATE POLICY "Creators and admins can delete folders" ON folders FOR DELETE USING (
  created_by = auth.uid() OR
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Projects policies
CREATE POLICY "Users can view all projects" ON projects FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create projects" ON projects FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Creators and admins can update projects" ON projects FOR UPDATE USING (
  created_by = auth.uid() OR
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);
CREATE POLICY "Creators and admins can delete projects" ON projects FOR DELETE USING (
  created_by = auth.uid() OR
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Kanban columns policies
CREATE POLICY "Users can view all kanban columns" ON kanban_columns FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create kanban columns" ON kanban_columns FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Creators and admins can update kanban columns" ON kanban_columns FOR UPDATE USING (
  created_by = auth.uid() OR
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);
CREATE POLICY "Creators and admins can delete kanban columns" ON kanban_columns FOR DELETE USING (
  created_by = auth.uid() OR
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Skillset groups policies
CREATE POLICY "Users can view all skillset groups" ON skillset_groups FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create skillset groups" ON skillset_groups FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Creators and admins can update skillset groups" ON skillset_groups FOR UPDATE USING (
  created_by = auth.uid() OR
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);
CREATE POLICY "Creators and admins can delete skillset groups" ON skillset_groups FOR DELETE USING (
  created_by = auth.uid() OR
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Tasks policies
CREATE POLICY "Users can view all tasks" ON tasks FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create tasks" ON tasks FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Assigned users, creators and admins can update tasks" ON tasks FOR UPDATE USING (
  created_by = auth.uid() OR
  assigned_user_id = auth.uid() OR
  owner_id = auth.uid() OR
  auth.uid()::text = ANY(assigned_users) OR
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);
CREATE POLICY "Creators and admins can delete tasks" ON tasks FOR DELETE USING (
  created_by = auth.uid() OR
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Task comments policies
CREATE POLICY "Users can view all task comments" ON task_comments FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create task comments" ON task_comments FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Comment authors and admins can update comments" ON task_comments FOR UPDATE USING (
  user_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);
CREATE POLICY "Comment authors and admins can delete comments" ON task_comments FOR DELETE USING (
  user_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Task history policies
CREATE POLICY "Users can view all task history" ON task_history FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create task history" ON task_history FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- User capacities policies
CREATE POLICY "Users can view all user capacities" ON user_capacities FOR SELECT USING (true);
CREATE POLICY "Users can manage own capacity" ON user_capacities FOR ALL USING (user_id = auth.uid());
CREATE POLICY "Admins can manage all capacities" ON user_capacities FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Task efforts policies
CREATE POLICY "Users can view all task efforts" ON task_efforts FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create task efforts" ON task_efforts FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Assigned users and admins can update task efforts" ON task_efforts FOR UPDATE USING (
  assigned_user_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);
CREATE POLICY "Admins can delete task efforts" ON task_efforts FOR DELETE USING (
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Function to handle user profile creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_profiles (id, email, name, role)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'name', split_part(NEW.email, '@', 1)),
    CASE
      WHEN NEW.email = '<EMAIL>' THEN 'admin'::user_role
      ELSE 'user'::user_role
    END
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create user profile on signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Insert default data
INSERT INTO user_groups (id, name, color, created_by) VALUES
  ('devs', 'Developers', 'bg-blue-500', (SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1)),
  ('pm', 'Project Managers', 'bg-green-500', (SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1)),
  ('cm', 'Campaign Managers', 'bg-purple-500', (SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1)),
  ('leaders', 'Team Leaders', 'bg-red-500', (SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1))
ON CONFLICT (id) DO NOTHING;

INSERT INTO kanban_columns (id, title, color, position, created_by) VALUES
  ('todo', 'To Do', 'bg-gray-100', 0, (SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1)),
  ('in-progress', 'In Progress', 'bg-blue-50', 1, (SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1)),
  ('review', 'Review', 'bg-yellow-50', 2, (SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1)),
  ('done', 'Done', 'bg-green-50', 3, (SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1))
ON CONFLICT (id) DO NOTHING;
