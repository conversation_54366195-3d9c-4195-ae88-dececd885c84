# Supabase Setup Guide

This guide will help you set up Supabase for your TaskFlow project management application.

## Prerequisites

- A Supabase account (sign up at [supabase.com](https://supabase.com))
- Node.js and npm installed

## Step 1: Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign in
2. Click "New Project"
3. Choose your organization
4. Enter a project name (e.g., "taskflow-pm")
5. Enter a database password (save this securely)
6. Select a region close to your users
7. Click "Create new project"

## Step 2: Configure Environment Variables

1. Copy `.env.example` to `.env.local`:
   ```bash
   cp .env.example .env.local
   ```

2. In your Supabase project dashboard, go to Settings > API
3. Copy the following values to your `.env.local` file:
   ```
   VITE_SUPABASE_URL=your_project_url
   VITE_SUPABASE_ANON_KEY=your_anon_public_key
   ```

## Step 3: Set Up Database Schema

1. In your Supabase project dashboard, go to the SQL Editor
2. Copy the contents of `supabase-schema.sql` from this project
3. Paste it into the SQL Editor and run it
4. This will create all necessary tables, policies, and functions

## Step 4: Create Super Admin User

1. In your Supabase project dashboard, go to Authentication > Users
2. Click "Add user"
3. Enter:
   - Email: `<EMAIL>`
   - Password: `Vklonis123@$`
   - Email Confirm: Yes
4. Click "Create user"

The user will automatically be assigned admin role due to the database trigger.

## Step 5: Configure Authentication Settings

1. Go to Authentication > Settings
2. Under "Site URL", add your application URL (e.g., `http://localhost:5173` for development)
3. Under "Redirect URLs", add your application URL
4. Configure any additional auth providers if needed

## Step 6: Test the Setup

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Navigate to your application
3. Try logging in with the super admin credentials
4. Verify that you can create tasks, projects, and other data

## Security Notes

- Never commit your `.env.local` file to version control
- The anon key is safe to use in client-side code
- Row Level Security (RLS) policies protect your data
- Only authenticated users can access the application
- Admin users have additional privileges for system management

## Troubleshooting

### Common Issues

1. **"Missing Supabase environment variables" error**
   - Ensure `.env.local` file exists and contains correct values
   - Restart your development server after adding environment variables

2. **Authentication not working**
   - Check that your Site URL and Redirect URLs are configured correctly
   - Verify that the user exists in the Authentication > Users section

3. **Database errors**
   - Ensure the schema was applied correctly
   - Check the Supabase logs in the dashboard for detailed error messages

4. **RLS policy errors**
   - Verify that all RLS policies were created
   - Check that the user has the correct role assigned

### Getting Help

- Check the Supabase documentation: [docs.supabase.com](https://docs.supabase.com)
- Review the application logs in the browser console
- Check the Supabase project logs in the dashboard

## Production Deployment

When deploying to production:

1. Update environment variables with production Supabase project details
2. Configure proper domain settings in Supabase Authentication
3. Review and test all RLS policies
4. Set up proper backup and monitoring
5. Consider enabling additional security features like email verification

## Data Migration

If you have existing data from the local version:

1. Export your data using the Settings > Backup & Restore feature
2. Set up the new Supabase-enabled version
3. Import your data using the same Backup & Restore feature
4. Verify all data was imported correctly

The import/export functionality maintains compatibility between local and Supabase versions.
