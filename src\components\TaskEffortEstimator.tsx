import React, { useState, useEffect } from 'react';
import { useStore } from '../store/useStore';
import { Clock, User, Palette, Calculator } from 'lucide-react';
import { TaskEffort, TaskDuration } from '../types';
import { differenceInMinutes } from 'date-fns';

interface TaskEffortEstimatorProps {
  taskId?: string;
  initialEffort?: TaskEffort;
  onEffortChange: (effort: TaskEffort) => void;
  disabled?: boolean;
  durations?: TaskDuration[];
  currentStatus?: string;
}

export default function TaskEffortEstimator({
  taskId,
  initialEffort,
  onEffortChange,
  disabled = false,
  durations = [],
  currentStatus
}: TaskEffortEstimatorProps) {
  const { users, skillsetGroups, columns } = useStore();

  // Calculate total time spent in "in-progress" status
  const calculateInProgressHours = (taskDurations: TaskDuration[], currentTaskStatus?: string): number => {
    if (!taskDurations || taskDurations.length === 0) return 0;

    // Find the "in-progress" status ID from columns
    const inProgressColumn = columns.find(col =>
      col.title.toLowerCase().includes('progress') ||
      col.id === 'in-progress' ||
      col.title.toLowerCase() === 'in progress'
    );

    if (!inProgressColumn) return 0;

    let totalMinutes = 0;

    taskDurations.forEach(duration => {
      if (duration.status === inProgressColumn.id) {
        const start = new Date(duration.startTime);
        const end = duration.endTime ? new Date(duration.endTime) :
          (currentTaskStatus === inProgressColumn.id ? new Date() : start);

        totalMinutes += differenceInMinutes(end, start);
      }
    });

    // Convert minutes to hours (rounded to 1 decimal place)
    return Math.round((totalMinutes / 60) * 10) / 10;
  };

  // Calculate actual hours from in-progress time
  const calculatedActualHours = calculateInProgressHours(durations, currentStatus);

  const [effort, setEffort] = useState<TaskEffort>({
    taskId: taskId || '',
    estimatedHours: initialEffort?.estimatedHours || 0,
    actualHours: calculatedActualHours, // Use calculated hours instead of initial
    assignedUserId: initialEffort?.assignedUserId || undefined,
    requiredSkillsets: initialEffort?.requiredSkillsets || [],
  });

  // Update effort when props change
  useEffect(() => {
    const newCalculatedHours = calculateInProgressHours(durations, currentStatus);

    if (initialEffort) {
      setEffort({
        ...initialEffort,
        actualHours: newCalculatedHours // Always use calculated hours
      });
    } else {
      setEffort(prev => ({
        ...prev,
        actualHours: newCalculatedHours
      }));
    }
  }, [initialEffort, durations, currentStatus]);

  // Update effort when durations change (real-time updates)
  useEffect(() => {
    const newCalculatedHours = calculateInProgressHours(durations, currentStatus);
    setEffort(prev => ({
      ...prev,
      actualHours: newCalculatedHours
    }));

    // Notify parent of the change
    onEffortChange({
      ...effort,
      actualHours: newCalculatedHours
    });
  }, [durations, currentStatus]);

  // Update effort when form changes
  const updateEffort = (field: keyof TaskEffort, value: any) => {
    const updatedEffort = { ...effort, [field]: value };
    setEffort(updatedEffort);
    onEffortChange(updatedEffort);
  };

  const toggleSkillset = (skillsetId: string) => {
    const newSkillsets = effort.requiredSkillsets.includes(skillsetId)
      ? effort.requiredSkillsets.filter(id => id !== skillsetId)
      : [...effort.requiredSkillsets, skillsetId];

    updateEffort('requiredSkillsets', newSkillsets);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Clock className="w-4 h-4" />
        <h4 className="font-medium">Effort Estimation</h4>
      </div>

      <div className="bg-gray-50 p-4 rounded-lg border">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Estimated Hours */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Estimated Hours
            </label>
            <input
              type="number"
              min="0"
              step="0.5"
              value={effort.estimatedHours}
              onChange={(e) => updateEffort('estimatedHours', parseFloat(e.target.value) || 0)}
              disabled={disabled}
              className="w-full border rounded-lg p-2 disabled:bg-gray-100"
              placeholder="0"
            />
          </div>

          {/* Actual Hours */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
              <Calculator className="w-4 h-4" />
              Actual Hours
              <span className="text-xs text-blue-600 font-normal">(Auto-calculated)</span>
            </label>
            <input
              type="number"
              min="0"
              step="0.1"
              value={effort.actualHours || 0}
              readOnly
              className="w-full border rounded-lg p-2 bg-blue-50 border-blue-200 text-blue-900 cursor-not-allowed"
              placeholder="0"
              title="Automatically calculated from time spent in 'In Progress' status"
            />
            <p className="text-xs text-blue-600 mt-1">
              Based on time in "In Progress" status
            </p>
          </div>

          {/* Assigned User */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
              <User className="w-4 h-4" />
              Assigned User
            </label>
            <select
              value={effort.assignedUserId || ''}
              onChange={(e) => updateEffort('assignedUserId', e.target.value || undefined)}
              disabled={disabled}
              className="w-full border rounded-lg p-2 disabled:bg-gray-100"
            >
              <option value="">Unassigned</option>
              {users.map(user => (
                <option key={user.id} value={user.id}>
                  {user.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Required Skillsets */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <label className="block text-sm font-medium text-gray-700 mb-3 flex items-center gap-1">
            <Palette className="w-4 h-4" />
            Required Skillsets
          </label>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
            {skillsetGroups.map(skillset => (
              <label key={skillset.id} className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={effort.requiredSkillsets.includes(skillset.id)}
                  onChange={() => toggleSkillset(skillset.id)}
                  disabled={disabled}
                  className="rounded"
                />
                <div className="flex items-center gap-1">
                  <div className={`w-3 h-3 rounded-full ${skillset.color}`} />
                  <span className="text-sm">{skillset.name}</span>
                </div>
              </label>
            ))}
          </div>
          {effort.requiredSkillsets.length === 0 && (
            <p className="text-xs text-gray-500 mt-2">No skillsets selected. This task can be completed by anyone.</p>
          )}
        </div>

        {/* Summary */}
        {(effort.estimatedHours > 0 || effort.actualHours > 0) && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Estimated:</span>
                <span className="font-medium">{effort.estimatedHours.toFixed(1)}h</span>
              </div>
              {effort.actualHours > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Actual:</span>
                  <span className="font-medium">{effort.actualHours.toFixed(1)}h</span>
                </div>
              )}
              {effort.estimatedHours > 0 && effort.actualHours > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Variance:</span>
                  <span className={`font-medium ${effort.actualHours > effort.estimatedHours ? 'text-red-600' : 'text-green-600'}`}>
                    {effort.actualHours > effort.estimatedHours ? '+' : ''}
                    {(effort.actualHours - effort.estimatedHours).toFixed(1)}h
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
