import React from 'react';
import { TaskDuration } from '../types';
import { Clock } from 'lucide-react';
import { formatDistanceToNowStrict, differenceInHours, differenceInMinutes } from 'date-fns';
import { useStore } from '../store/useStore';

interface TaskDurationStatsProps {
  durations: TaskDuration[];
  currentStatus: string;
}

export default function TaskDurationStats({ durations, currentStatus }: TaskDurationStatsProps) {
  const { columns } = useStore();

  const formatDuration = (startDate: Date, endDate: Date) => {
    const hours = differenceInHours(endDate, startDate);
    const minutes = differenceInMinutes(endDate, startDate) % 60;

    if (hours === 0) {
      return `${minutes}m`;
    }
    return `${hours}h ${minutes}m`;
  };

  // Handle undefined durations
  if (!durations || !Array.isArray(durations)) {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Clock className="w-5 h-5" />
          Duration Stats
        </h3>
        <div className="text-center text-gray-500 py-4">
          No duration data available
        </div>
      </div>
    );
  }

  // Group durations by status
  const durationsByStatus = durations.reduce((acc, duration) => {
    if (!acc[duration.status]) {
      acc[duration.status] = [];
    }
    acc[duration.status].push(duration);
    return acc;
  }, {} as Record<string, TaskDuration[]>);

  // Calculate total time per status
  const totalTimeByStatus = Object.entries(durationsByStatus).map(([statusId, statusDurations]) => {
    let totalMinutes = 0;

    statusDurations.forEach(duration => {
      const start = new Date(duration.startTime);
      const end = duration.endTime ? new Date(duration.endTime) : 
        (statusId === currentStatus ? new Date() : start);
      
      totalMinutes += differenceInMinutes(end, start);
    });

    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;

    const column = columns.find(col => col.id === statusId);
    const statusLabel = column ? column.title : statusId;

    return {
      statusId,
      statusLabel,
      totalTime: hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`
    };
  });

  const getStatusColor = (statusId: string) => {
    const column = columns.find(col => col.id === statusId);
    return column?.color || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="mb-6">
      <h3 className="text-lg font-semibold flex items-center gap-2 mb-3">
        <Clock className="w-5 h-5" />
        Time in Status
      </h3>
      
      <div className="grid grid-cols-4 gap-4">
        {totalTimeByStatus.map(({ statusId, statusLabel, totalTime }) => (
          <div key={statusId} className="p-3 rounded-lg bg-gray-50">
            <div className="flex items-center gap-2 mb-1">
              <span className={`px-2 py-1 rounded text-xs ${getStatusColor(statusId)}`}>
                {statusLabel}
              </span>
            </div>
            <p className="text-lg font-semibold">{totalTime}</p>
          </div>
        ))}
      </div>
    </div>
  );
}