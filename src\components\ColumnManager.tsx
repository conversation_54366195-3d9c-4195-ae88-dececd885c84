import React, { useState } from 'react';
import { useStore } from '../store/useStore';
import { Plus, Edit2, Trash2, GripVertical } from 'lucide-react';
import { KanbanColumn } from '../types';

const COLORS = [
  'bg-gray-100',
  'bg-blue-50',
  'bg-yellow-50',
  'bg-green-50',
  'bg-purple-50',
  'bg-pink-50',
];

export default function ColumnManager() {
  const { columns, addColumn, updateColumn, deleteColumn, reorderColumns } = useStore();
  const [showForm, setShowForm] = useState(false);
  const [editingColumn, setEditingColumn] = useState<KanbanColumn | null>(null);
  const [editingTitle, setEditingTitle] = useState<string>('');
  const [formData, setFormData] = useState({
    title: '',
    color: COLORS[0],
  });
  const [draggedColumn, setDraggedColumn] = useState<KanbanColumn | null>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editingColumn) {
      updateColumn(editingColumn.id, formData);
      setEditingColumn(null);
    } else {
      addColumn(formData);
    }
    setFormData({ title: '', color: COLORS[0] });
    setShowForm(false);
  };

  const handleDragStart = (column: KanbanColumn) => {
    setDraggedColumn(column);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (targetColumn: KanbanColumn) => {
    if (!draggedColumn || draggedColumn.id === targetColumn.id) return;

    const newColumns = [...columns];
    const draggedIndex = newColumns.findIndex(col => col.id === draggedColumn.id);
    const targetIndex = newColumns.findIndex(col => col.id === targetColumn.id);

    newColumns.splice(draggedIndex, 1);
    newColumns.splice(targetIndex, 0, draggedColumn);

    reorderColumns(newColumns);
    setDraggedColumn(null);
  };

  const startEditing = (column: KanbanColumn) => {
    setEditingTitle(column.title);
    setEditingColumn(column);
  };

  const handleTitleUpdate = (columnId: string) => {
    if (editingTitle.trim()) {
      updateColumn(columnId, { title: editingTitle });
      setEditingColumn(null);
      setEditingTitle('');
    }
  };

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-white">Kanban Columns</h2>
        <button
          onClick={() => setShowForm(true)}
          className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          <Plus className="w-5 h-5" />
        </button>
      </div>

      {showForm && (
        <form onSubmit={handleSubmit} className="bg-gray-800 p-4 rounded-lg shadow-sm mb-4">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-white mb-1">Title</label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-white mb-1">Color</label>
              <div className="flex gap-2">
                {COLORS.map((color) => (
                  <button
                    key={color}
                    type="button"
                    onClick={() => setFormData({ ...formData, color })}
                    className={`w-8 h-8 rounded-lg ${color} ${
                      formData.color === color ? 'ring-2 ring-offset-2 ring-blue-600' : ''
                    }`}
                  />
                ))}
              </div>
            </div>
            
            <div className="flex justify-end gap-2">
              <button
                type="button"
                onClick={() => {
                  setShowForm(false);
                  setEditingColumn(null);
                }}
                className="px-4 py-2 text-gray-300 hover:bg-gray-700 rounded-lg"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                {editingColumn ? 'Update' : 'Create'} Column
              </button>
            </div>
          </div>
        </form>
      )}

      <div className="space-y-2">
        {columns.map((column) => (
          <div
            key={column.id}
            draggable
            onDragStart={() => handleDragStart(column)}
            onDragOver={handleDragOver}
            onDrop={() => handleDrop(column)}
            className="flex items-center justify-between p-3 bg-gray-800 rounded-lg shadow-sm cursor-move text-white"
          >
            <div className="flex items-center gap-3">
              <GripVertical className="w-5 h-5 text-gray-400" />
              <div className={`w-4 h-4 rounded ${column.color}`} />
              {editingColumn?.id === column.id ? (
                <input
                  type="text"
                  value={editingTitle}
                  onChange={(e) => setEditingTitle(e.target.value)}
                  onBlur={() => handleTitleUpdate(column.id)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      handleTitleUpdate(column.id);
                    }
                  }}
                  className="bg-gray-700 text-white px-2 py-1 rounded"
                  autoFocus
                />
              ) : (
                <span className="font-medium">{column.title}</span>
              )}
            </div>
            
            <div className="flex gap-2">
              <button
                onClick={() => startEditing(column)}
                className="p-1 hover:bg-gray-700 rounded"
              >
                <Edit2 className="w-4 h-4" />
              </button>
              <button
                onClick={() => deleteColumn(column.id)}
                className="p-1 hover:bg-gray-700 rounded text-red-400"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}