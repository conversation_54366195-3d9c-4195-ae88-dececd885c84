import React, { useState } from 'react';
import { Plus } from 'lucide-react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import TaskTreeNode from './TaskTreeNode';
import TaskForm from './TaskForm';
import ProjectForm from './ProjectForm';
import FolderForm from './FolderForm';

export default function TaskTreeSidebar() {
  const {
    projects,
    folders,
    tasks,
    selectedTreeNode,
    expandedTreeNodes,
    setSelectedTreeNode,
    toggleTreeNode,
    addTask,
    addProject,
    addFolder
  } = useSupabaseStore();

  const [showTaskForm, setShowTaskForm] = useState(false);
  const [showProjectForm, setShowProjectForm] = useState(false);
  const [showFolderForm, setShowFolderForm] = useState(false);
  const [contextMenuTarget, setContextMenuTarget] = useState<string | null>(null);

  // Build hierarchical structure
  const rootFolders = folders.filter(f => !f.parentId);
  const rootProjects = projects.filter(p => !p.folderId);

  const getChildFolders = (parentId: string) => 
    folders.filter(f => f.parentId === parentId);
  
  const getProjectsInFolder = (folderId: string) => {
    const projectsInFolder = projects.filter(p => p.folderId === folderId);
    return projectsInFolder;
  };

  const getTasksInProject = (projectId: string) =>
    tasks.filter(t => t.projectId === projectId);

  const handleAddProject = () => {
    setContextMenuTarget(null);
    setShowProjectForm(true);
  };

  const handleAddFolder = () => {
    setContextMenuTarget(null);
    setShowFolderForm(true);
  };

  const handleAddTask = (projectId?: string) => {
    setContextMenuTarget(projectId || null);
    setShowTaskForm(true);
  };

  const handleContextAction = (nodeId: string, action: 'addTask' | 'addProject' | 'addFolder') => {
    const node = [...projects, ...folders].find(item => item.id === nodeId);
    
    switch (action) {
      case 'addTask':
        // Only allow adding tasks to projects
        if (projects.find(p => p.id === nodeId)) {
          handleAddTask(nodeId);
        }
        break;
      case 'addProject':
        // Add project to folder or root
        setContextMenuTarget(folders.find(f => f.id === nodeId) ? nodeId : null);
        setShowProjectForm(true);
        break;
      case 'addFolder':
        // Add subfolder to folder or root
        setContextMenuTarget(folders.find(f => f.id === nodeId) ? nodeId : null);
        setShowFolderForm(true);
        break;
    }
  };

  const renderFolderContent = (folderId: string, level: number) => {
    const childFolders = getChildFolders(folderId);
    const projectsInFolder = getProjectsInFolder(folderId);
    
    return (
      <>
        {/* Child folders */}
        {childFolders.map(childFolder => (
          <TaskTreeNode
            key={childFolder.id}
            id={childFolder.id}
            type="folder"
            name={childFolder.name}
            level={level}
            isExpanded={expandedTreeNodes.has(childFolder.id)}
            isSelected={selectedTreeNode === childFolder.id}
            onToggle={() => toggleTreeNode(childFolder.id)}
            onSelect={() => setSelectedTreeNode(childFolder.id)}
            onContextAction={handleContextAction}
          >
            {expandedTreeNodes.has(childFolder.id) && renderFolderContent(childFolder.id, level + 1)}
          </TaskTreeNode>
        ))}
        
        {/* Projects in this folder */}
        {projectsInFolder.map(project => (
          <TaskTreeNode
            key={project.id}
            id={project.id}
            type="project"
            name={project.name}
            level={level}
            color={project.color}
            isExpanded={expandedTreeNodes.has(project.id)}
            isSelected={selectedTreeNode === project.id}
            onToggle={() => toggleTreeNode(project.id)}
            onSelect={() => setSelectedTreeNode(project.id)}
            onContextAction={handleContextAction}
          >
            {expandedTreeNodes.has(project.id) && getTasksInProject(project.id).map(task => (
              <TaskTreeNode
                key={task.id}
                id={task.id}
                type="task"
                name={task.title}
                level={level + 1}
                status={task.status}
                isSelected={selectedTreeNode === task.id}
                onSelect={() => setSelectedTreeNode(task.id)}
              />
            ))}
          </TaskTreeNode>
        ))}
      </>
    );
  };

  return (
    <>
      <div className="w-80 bg-white border-r border-gray-200 overflow-y-auto">
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold">Projects</h2>
            <button 
              onClick={handleAddProject}
              className="p-1 hover:bg-gray-100 rounded"
              title="Add Project"
            >
              <Plus className="w-4 h-4" />
            </button>
          </div>

          <div className="space-y-1">
            {/* Root folders */}
            {rootFolders.map(folder => (
              <TaskTreeNode
                key={folder.id}
                id={folder.id}
                type="folder"
                name={folder.name}
                level={0}
                isExpanded={expandedTreeNodes.has(folder.id)}
                isSelected={selectedTreeNode === folder.id}
                onToggle={() => toggleTreeNode(folder.id)}
                onSelect={() => setSelectedTreeNode(folder.id)}
                onContextAction={handleContextAction}
              >
                {expandedTreeNodes.has(folder.id) && renderFolderContent(folder.id, 1)}
              </TaskTreeNode>
            ))}

            {/* Root projects (projects not in any folder) */}
            {rootProjects.map(project => (
              <TaskTreeNode
                key={project.id}
                id={project.id}
                type="project"
                name={project.name}
                level={0}
                color={project.color}
                isExpanded={expandedTreeNodes.has(project.id)}
                isSelected={selectedTreeNode === project.id}
                onToggle={() => toggleTreeNode(project.id)}
                onSelect={() => setSelectedTreeNode(project.id)}
                onContextAction={handleContextAction}
              >
                {expandedTreeNodes.has(project.id) && getTasksInProject(project.id).map(task => (
                  <TaskTreeNode
                    key={task.id}
                    id={task.id}
                    type="task"
                    name={task.title}
                    level={1}
                    status={task.status}
                    isSelected={selectedTreeNode === task.id}
                    onSelect={() => setSelectedTreeNode(task.id)}
                  />
                ))}
              </TaskTreeNode>
            ))}
          </div>
        </div>
      </div>

      {/* Forms */}
      {showTaskForm && (
        <TaskForm
          onSubmit={(taskData) => {
            if (contextMenuTarget) {
              taskData.projectId = contextMenuTarget;
            }
            addTask(taskData);
            setShowTaskForm(false);
            setContextMenuTarget(null);
          }}
          onClose={() => {
            setShowTaskForm(false);
            setContextMenuTarget(null);
          }}
        />
      )}

      {showProjectForm && (
        <ProjectForm
          onSubmit={(projectData) => {
            if (contextMenuTarget) {
              projectData.folderId = contextMenuTarget;
            }
            addProject(projectData);
            setShowProjectForm(false);
            setContextMenuTarget(null);
          }}
          onClose={() => {
            setShowProjectForm(false);
            setContextMenuTarget(null);
          }}
        />
      )}

      {showFolderForm && (
        <FolderForm
          onSubmit={(folderData) => {
            if (contextMenuTarget) {
              folderData.parentId = contextMenuTarget;
            }
            addFolder(folderData);
            setShowFolderForm(false);
            setContextMenuTarget(null);
          }}
          onClose={() => {
            setShowFolderForm(false);
            setContextMenuTarget(null);
          }}
        />
      )}
    </>
  );
}








