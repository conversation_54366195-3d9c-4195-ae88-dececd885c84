import React, { useState } from 'react';
import { ChevronRight, ChevronDown, Folder, FolderOpen, FileText, Plus, MoreVertical } from 'lucide-react';
import { Task } from '../types';

interface TaskTreeNodeProps {
  id: string;
  type: 'folder' | 'project' | 'task';
  name: string;
  level: number;
  color?: string;
  status?: Task['status'];
  isExpanded?: boolean;
  isSelected?: boolean;
  onToggle?: () => void;
  onSelect?: () => void;
  onContextAction?: (nodeId: string, action: 'addTask' | 'addProject' | 'addFolder') => void;
  children?: React.ReactNode;
}

export default function TaskTreeNode({
  id,
  type,
  name,
  level,
  color,
  status,
  isExpanded = false,
  isSelected = false,
  onToggle,
  onSelect,
  onContextAction,
  children
}: TaskTreeNodeProps) {
  const [showContextMenu, setShowContextMenu] = useState(false);
  const hasChildren = React.Children.count(children) > 0;

  const getIcon = () => {
    switch (type) {
      case 'folder':
        return isExpanded ? <FolderOpen className="w-4 h-4" /> : <Folder className="w-4 h-4" />;
      case 'project':
        return <div className={`w-3 h-3 rounded-full ${color || 'bg-blue-500'}`} />;
      case 'task':
        return <FileText className="w-4 h-4" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'todo': return 'text-gray-500';
      case 'in-progress': return 'text-blue-500';
      case 'done': return 'text-green-500';
      default: return 'text-gray-500';
    }
  };

  const getContextMenuItems = () => {
    switch (type) {
      case 'folder':
        return [
          { label: 'Add Project', action: 'addProject' as const },
          { label: 'Add Subfolder', action: 'addFolder' as const }
        ];
      case 'project':
        return [
          { label: 'Add Task', action: 'addTask' as const }
        ];
      case 'task':
        return [];
      default:
        return [];
    }
  };

  return (
    <div className="relative">
      <div
        className={`flex items-center gap-2 px-2 py-1 rounded cursor-pointer hover:bg-gray-50 group ${
          isSelected ? 'bg-blue-50 text-blue-700' : ''
        }`}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
        onClick={onSelect}
      >
        {/* Expand/collapse button */}
        {hasChildren && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggle?.();
            }}
            className="p-0.5 hover:bg-gray-200 rounded"
          >
            {isExpanded ? (
              <ChevronDown className="w-3 h-3" />
            ) : (
              <ChevronRight className="w-3 h-3" />
            )}
          </button>
        )}
        
        {/* Icon */}
        <div className={`flex-shrink-0 ${type === 'task' ? getStatusColor() : ''}`}>
          {getIcon()}
        </div>
        
        {/* Name */}
        <span className="flex-1 text-sm truncate">{name}</span>
        
        {/* Context menu button */}
        {onContextAction && getContextMenuItems().length > 0 && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowContextMenu(!showContextMenu);
            }}
            className="p-1 opacity-0 group-hover:opacity-100 hover:bg-gray-200 rounded"
          >
            <MoreVertical className="w-3 h-3" />
          </button>
        )}
      </div>

      {/* Context menu */}
      {showContextMenu && onContextAction && (
        <div className="absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-50 min-w-[120px]">
          {getContextMenuItems().map((item) => (
            <button
              key={item.action}
              onClick={(e) => {
                e.stopPropagation();
                onContextAction(id, item.action);
                setShowContextMenu(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 flex items-center gap-2"
            >
              <Plus className="w-3 h-3" />
              {item.label}
            </button>
          ))}
        </div>
      )}
      
      {/* Children */}
      {isExpanded && children && (
        <div>
          {children}
        </div>
      )}
    </div>
  );
}



